# Copyright (c) 2022-2025, The Isaac Lab Project Developers (https://github.com/isaac-sim/IsaacLab/blob/main/CONTRIBUTORS.md).
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

"""Isaac Lab环境的共享测试实用工具。

该模块提供了用于测试Isaac Lab环境的实用函数，包括环境设置、
随机动作测试和数据验证等功能。它支持单智能体和多智能体环境的测试。
"""

import gymnasium as gym
import inspect
import os
import torch

import carb
import omni.usd
import pytest
from isaacsim.core.version import get_version

from isaaclab.envs.utils.spaces import sample_space

from isaaclab_tasks.utils.parse_cfg import parse_env_cfg


def setup_environment(
    include_play: bool = False,
    factory_envs: bool | None = None,
    multi_agent: bool | None = None,
) -> list[str]:
    """
    获取所有已注册的Isaac环境任务ID，并应用可选过滤器。

    该函数扫描gym注册表中的所有Isaac环境，并根据提供的过滤器条件
    返回匹配的环境列表。这对于批量测试环境非常有用。

    Args:
        include_play: 如果为True，包含以'Play-v0'结尾的环境。
        factory_envs:
            - True: 仅包含Factory环境
            - False: 排除Factory环境
            - None: 包含Factory和非Factory环境
        multi_agent:
            - True: 仅包含多智能体环境
            - False: 仅包含单智能体环境
            - None: 包含所有环境，无论智能体类型如何

    Returns:
        匹配所选过滤器的任务ID的排序列表。
    """
    # 为自动化环境禁用wandb的交互模式
    os.environ["WANDB_DISABLED"] = "true"

    # 获取所有Isaac环境名称
    registered_tasks = []
    for task_spec in gym.registry.values():
        # 仅考虑Isaac环境
        if "Isaac" not in task_spec.id:
            continue

        # 如果需要，过滤Play环境
        if not include_play and task_spec.id.endswith("Play-v0"):
            continue

        # TODO: factory环境如果与其他环境一起运行会导致测试失败，
        # 所以我们单独收集这些环境以在单独的单元测试中运行。
        # 应用factory过滤器
        if (factory_envs is True and ("Factory" not in task_spec.id and "Forge" not in task_spec.id)) or (
            factory_envs is False and ("Factory" in task_spec.id or "Forge" in task_spec.id)
        ):
            continue
        # 如果为None：无过滤器

        # 应用多智能体过滤器
        if multi_agent is not None:
            # 解析配置
            env_cfg = parse_env_cfg(task_spec.id)
            if (multi_agent is True and not hasattr(env_cfg, "possible_agents")) or (
                multi_agent is False and hasattr(env_cfg, "possible_agents")
            ):
                continue
        # 如果为None：无过滤器

        registered_tasks.append(task_spec.id)

    # 按字母顺序对环境进行排序
    registered_tasks.sort()

    # 此标志是必要的，以防止在运行许多环境时仿真随机卡住的错误
    carb.settings.get_settings().set_bool("/physics/cooking/ujitsoCollisionCooking", False)

    print(">>> 所有已注册的环境:", registered_tasks)

    return registered_tasks


def _run_environments(
    task_name,
    device,
    num_envs,
    num_steps=100,
    multi_agent=False,
    create_stage_in_memory=False,
    disable_clone_in_fabric=False,
):
    """运行所有环境并检查环境返回有效信号。

    该函数创建指定的环境，运行随机动作，并验证环境返回的数据是否有效。
    它是环境测试的核心函数。

    Args:
        task_name: 环境的名称。
        device: 要使用的设备（例如，'cuda'）。
        num_envs: 环境数量。
        num_steps: 仿真步数。
        multi_agent: 环境是否为多智能体。
        create_stage_in_memory: 是否在内存中创建阶段。
        disable_clone_in_fabric: 是否禁用fabric克隆。
    """

    # 如果不支持内存中的阶段，则跳过测试
    isaac_sim_version = float(".".join(get_version()[2]))
    if isaac_sim_version < 5 and create_stage_in_memory:
        pytest.skip("此版本的Isaac Sim不支持内存中的阶段")

    # 跳过吸盘夹具环境，因为它们需要CPU仿真，无法使用GPU仿真运行
    if "Suction" in task_name and device != "cpu":
        return

    # 跳过这些环境，因为它们无法在合理的VRAM内运行32个环境
    if num_envs == 32 and task_name in [
        "Isaac-Stack-Cube-Franka-IK-Rel-Blueprint-v0",
        "Isaac-Stack-Cube-Instance-Randomize-Franka-IK-Rel-v0",
        "Isaac-Stack-Cube-Instance-Randomize-Franka-v0",
        "Isaac-Stack-Cube-Franka-IK-Rel-Visuomotor-v0",
        "Isaac-Stack-Cube-Franka-IK-Rel-Visuomotor-Cosmos-v0",
    ]:
        return

    # 跳过自动化环境，因为它们需要cuda安装
    if task_name in ["Isaac-AutoMate-Assembly-Direct-v0", "Isaac-AutoMate-Disassembly-Direct-v0"]:
        return

    # 检查这是否是泰迪熊环境，以及是否从正确的测试文件调用
    if task_name == "Isaac-Lift-Teddy-Bear-Franka-IK-Abs-v0":
        # 获取调用帧以检查哪个测试文件正在调用此函数
        frame = inspect.currentframe()
        while frame:
            filename = frame.f_code.co_filename
            if "test_lift_teddy_bear.py" in filename:
                # 从专用测试文件调用，允许运行
                break
            frame = frame.f_back

        # 如果不是从专用测试文件调用，则跳过
        if not frame:
            return

    print(f""">>> 为环境运行测试: {task_name}""")
    _check_random_actions(
        task_name,
        device,
        num_envs,
        num_steps=num_steps,
        multi_agent=multi_agent,
        create_stage_in_memory=create_stage_in_memory,
        disable_clone_in_fabric=disable_clone_in_fabric,
    )
    print(f""">>> 关闭环境: {task_name}""")
    print("-" * 80)


def _check_random_actions(
    task_name: str,
    device: str,
    num_envs: int,
    num_steps: int = 100,
    multi_agent: bool = False,
    create_stage_in_memory: bool = False,
    disable_clone_in_fabric: bool = False,
):
    """运行随机动作并检查环境返回有效信号。

    该函数是环境测试的核心，它创建环境、执行随机动作并验证返回的数据。
    它确保环境能够正确处理动作并返回有效的观察、奖励和其他信息。

    Args:
        task_name: 环境的名称。
        device: 要使用的设备（例如，'cuda'）。
        num_envs: 环境数量。
        num_steps: 仿真步数。
        multi_agent: 环境是否为多智能体。
        create_stage_in_memory: 是否在内存中创建阶段。
        disable_clone_in_fabric: 是否禁用fabric克隆。
    """
    # 如果未启用内存中的阶段，则创建新的上下文阶段
    if not create_stage_in_memory:
        omni.usd.get_context().new_stage()

    # 将rtx传感器carb设置重置为False
    carb.settings.get_settings().set_bool("/isaaclab/render/rtx_sensors", False)
    try:
        # 解析配置
        env_cfg = parse_env_cfg(task_name, device=device, num_envs=num_envs)
        # 设置配置参数
        env_cfg.sim.create_stage_in_memory = create_stage_in_memory
        if disable_clone_in_fabric:
            env_cfg.scene.clone_in_fabric = False

        # 根据多智能体模式进行过滤并创建环境
        if multi_agent:
            if not hasattr(env_cfg, "possible_agents"):
                print(f"[INFO]: 跳过{task_name}，因为它不是多智能体任务")
                return
            env = gym.make(task_name, cfg=env_cfg)
        else:
            if hasattr(env_cfg, "possible_agents"):
                print(f"[INFO]: 跳过{task_name}，因为它是多智能体任务")
                return
            env = gym.make(task_name, cfg=env_cfg)

    except Exception as e:
        # 在异常时尝试关闭环境
        if "env" in locals() and hasattr(env, "_is_closed"):
            env.close()
        else:
            if hasattr(e, "obj") and hasattr(e.obj, "_is_closed"):
                e.obj.close()
        pytest.fail(f"为任务{task_name}设置环境失败。错误：{e}")

    # 禁用停止时的控制
    env.unwrapped.sim._app_control_on_stop_handle = None  # type: ignore

    # 如果为`Isaac-Lift-Teddy-Bear-Franka-IK-Abs-v0`设置为inf，则覆盖动作空间
    if task_name == "Isaac-Lift-Teddy-Bear-Franka-IK-Abs-v0":
        for i in range(env.unwrapped.single_action_space.shape[0]):
            if env.unwrapped.single_action_space.low[i] == float("-inf"):
                env.unwrapped.single_action_space.low[i] = -1.0
            if env.unwrapped.single_action_space.high[i] == float("inf"):
                env.unwrapped.single_action_space.low[i] = 1.0

    # 重置环境
    obs, _ = env.reset()

    # 检查信号
    assert _check_valid_tensor(obs)

    # 仿真环境num_steps步
    with torch.inference_mode():
        for _ in range(num_steps):
            # 根据定义的空间采样动作
            if multi_agent:
                actions = {
                    agent: sample_space(
                        env.unwrapped.action_spaces[agent], device=env.unwrapped.device, batch_size=num_envs
                    )
                    for agent in env.unwrapped.possible_agents
                }
            else:
                actions = sample_space(
                    env.unwrapped.single_action_space, device=env.unwrapped.device, batch_size=num_envs
                )
            # 应用动作
            transition = env.step(actions)
            # 检查信号
            for data in transition[:-1]:  # 排除info
                if multi_agent:
                    for agent, agent_data in data.items():
                        assert _check_valid_tensor(agent_data), f"无效数据 ('{agent}'): {agent_data}"
                else:
                    assert _check_valid_tensor(data), f"无效数据: {data}"

    # 关闭环境
    env.close()


def _check_valid_tensor(data: torch.Tensor | dict) -> bool:
    """检查给定数据是否没有损坏的值。

    该函数递归检查张量、列表、元组和字典中的数据，确保没有NaN值。
    这对于验证环境返回的数据完整性非常重要。

    Args:
        data: 数据缓冲区。可以是张量、字典、列表或元组。

    Returns:
        如果数据有效则返回True。

    Raises:
        ValueError: 如果输入数据类型无效。
    """
    if isinstance(data, torch.Tensor):
        return not torch.any(torch.isnan(data))
    elif isinstance(data, (tuple, list)):
        return all(_check_valid_tensor(value) for value in data)
    elif isinstance(data, dict):
        return all(_check_valid_tensor(value) for value in data.values())
    else:
        raise ValueError(f"输入数据类型无效: {type(data)}。")
