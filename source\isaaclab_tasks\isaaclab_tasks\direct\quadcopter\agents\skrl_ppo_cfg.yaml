# Copyright (c) 2022-2025, The Isaac Lab Project Developers (https://github.com/isaac-sim/IsaacLab/blob/main/CONTRIBUTORS.md).
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

# 四旋翼无人机环境的SKRL PPO配置
# 该配置文件定义了使用SKRL库训练四旋翼无人机飞行控制策略的所有参数

seed: 42  # 随机种子，确保实验可重复性


# 模型配置 - 使用SKRL的模型实例化工具
# https://skrl.readthedocs.io/en/latest/api/utils/model_instantiators.html
models:
  separate: False  # 策略和价值网络不分离（共享特征提取层）

  # 策略网络配置（高斯分布模型）
  policy:
    class: GaussianMixin  # 高斯混合模型，适用于连续动作空间
    clip_actions: False  # 不在模型内部裁剪动作
    clip_log_std: True  # 裁剪对数标准差
    min_log_std: -20.0  # 最小对数标准差（防止标准差过小）
    max_log_std: 2.0  # 最大对数标准差（防止标准差过大）
    initial_log_std: 0.0  # 初始对数标准差
    network:
      - name: net
        input: STATES  # 输入状态
        layers: [64, 64]  # 两层64个神经元的隐藏层
        activations: elu  # ELU激活函数（适合连续控制）
    output: ACTIONS  # 输出动作

  # 价值网络配置（确定性模型）
  value:
    class: DeterministicMixin  # 确定性模型，输出单一价值
    clip_actions: False  # 不裁剪动作（价值网络不输出动作）
    network:
      - name: net
        input: STATES  # 输入状态
        layers: [64, 64]  # 两层64个神经元的隐藏层
        activations: elu  # ELU激活函数
    output: ONE  # 输出单一价值


# 回滚内存配置
# https://skrl.readthedocs.io/en/latest/api/memories/random.html
memory:
  class: RandomMemory  # 随机内存（用于存储经验）
  memory_size: -1  # 自动确定内存大小（与agent:rollouts相同）


# PPO智能体配置（字段名来自PPO_DEFAULT_CONFIG）
# https://skrl.readthedocs.io/en/latest/api/agents/ppo.html
agent:
  class: PPO  # PPO算法
  rollouts: 24  # 每次更新的回滚步数
  learning_epochs: 5  # 每次更新的学习轮数
  mini_batches: 4  # 小批次数量
  discount_factor: 0.99  # 折扣因子
  lambda: 0.95  # GAE lambda参数
  learning_rate: 5.0e-04  # 学习率
  learning_rate_scheduler: KLAdaptiveLR  # KL自适应学习率调度器
  learning_rate_scheduler_kwargs:
    kl_threshold: 0.016  # KL散度阈值
  state_preprocessor: RunningStandardScaler  # 状态预处理器（运行标准化）
  state_preprocessor_kwargs: null  # 状态预处理器参数
  value_preprocessor: RunningStandardScaler  # 价值预处理器（运行标准化）
  value_preprocessor_kwargs: null  # 价值预处理器参数
  random_timesteps: 0  # 随机时间步数
  learning_starts: 0  # 开始学习的时间步
  grad_norm_clip: 1.0  # 梯度范数裁剪
  ratio_clip: 0.2  # PPO比率裁剪参数
  value_clip: 0.2  # 价值裁剪参数
  clip_predicted_values: True  # 裁剪预测价值
  entropy_loss_scale: 0.0  # 熵损失缩放
  value_loss_scale: 1.0  # 价值损失缩放
  kl_threshold: 0.0  # KL散度阈值（用于早停）
  rewards_shaper_scale: 0.01  # 奖励塑形缩放
  time_limit_bootstrap: False  # 时间限制自举

  # 日志和检查点配置
  experiment:
    directory: "quadcopter_direct"  # 实验目录
    experiment_name: ""  # 实验名称
    write_interval: auto  # 写入间隔（自动）
    checkpoint_interval: auto  # 检查点间隔（自动）


# 顺序训练器配置
# https://skrl.readthedocs.io/en/latest/api/trainers/sequential.html
trainer:
  class: SequentialTrainer  # 顺序训练器
  timesteps: 4800  # 总训练时间步数
  environment_info: log  # 环境信息记录
