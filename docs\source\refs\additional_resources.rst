Additional Resources
====================

Here we provide external links to tools and various resources that you may also find useful.


Sim-to-Real Resources
---------------------

One of the core goals of the broader <PERSON> project is to bring real robots to life through the power of NVIDIA technology. There are many ways to do this, and thus, many tools that you could use.  These resources are dedicated to helping you navigate these possibilities by providing examples and discussions about closing the Sim-to-Real gap and deploying policies to actual real robots.

* `Closing the Sim-to-Real Gap: Training Spot Quadruped Locomotion with NVIDIA Isaac Lab <https://developer.nvidia.com/blog/closing-the-sim-to-real-gap-training-spot-quadruped-locomotion-with-nvidia-isaac-lab/>`_ is a detailed guide for training a quadruped locomotion policy for the Spot Quadruped from Boston Dynamics, and deploying it to the real robot.


LLM Generated Reward Functions
------------------------------

Our research endeavor, ``Eureka!``, has resulted in a pipeline for generating and tuning Reinforcement Learning (RL) reward functions using an LLM. These resources are dedicated to helping you utilize this pipeline to create RL based solutions to tasks that were once thought impossible!

* `<PERSON> Lab Eureka <https://github.com/isaac-sim/IsaacLabEureka>`_ is a github repository where you can setup your own LLM reward generation pipeline for your direct RL environments built in Isaac Lab!

* `Eureka! NVIDIA Research Breakthrough Puts New Spin on Robot Learning <https://blogs.nvidia.com/blog/eureka-robotics-research/>`_ is a blog post that covers the broad idea of this reward generation process.


Simulation Features
-------------------

At the heart of Isaac Lab is Isaac Sim, which is itself a feature rich tool that is useful for robotics in general, and not only for RL. The stronger your understanding of the simulation, the readily you will be able to exploit its capabilities for your own projects and applications. These resources are dedicated to informing you about the other features of the simulation that may be useful to you given your specific interest in Isaac Lab!

* `Simulation Performance Guide <https://docs.omniverse.nvidia.com/kit/docs/omni_physics/latest/dev_guide/guides/physics-performance.html>`_ is a best practice guide for obtaining the best simulation performance from OmniPhysics.

* `Deploying Policies in Isaac Sim <https://docs.isaacsim.omniverse.nvidia.com/latest/isaac_lab_tutorials/tutorial_policy_deployment.html>`_ is an Isaac Sim tutorial on how to use trained policies within the simulation.

* `Supercharge Robotics Workflows with AI and Simulation Using NVIDIA Isaac Sim 4.0 and NVIDIA Isaac Lab <https://developer.nvidia.com/blog/supercharge-robotics-workflows-with-ai-and-simulation-using-nvidia-isaac-sim-4-0-and-nvidia-isaac-lab/>`_ is a blog post covering the newest features of Isaac Sim 4.0, including ``pip install``, a more advanced physics engine, updated sensor simulations, and more!

* `Fast-Track Robot Learning in Simulation Using NVIDIA Isaac Lab <https://developer.nvidia.com/blog/fast-track-robot-learning-in-simulation-using-nvidia-isaac-lab/>`_ is a blog post covering the gamut of features for accelerated robot learning through Isaac Lab.
