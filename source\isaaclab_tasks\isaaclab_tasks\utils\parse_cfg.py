# Copyright (c) 2022-2025, The Isaac Lab Project Developers (https://github.com/isaac-sim/IsaacLab/blob/main/CONTRIBUTORS.md).
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

"""用于解析和加载配置的实用工具子模块。

该模块提供了从Gym注册表加载配置、解析环境配置和获取检查点路径的功能。
它支持YAML和Python配置文件，并提供了灵活的配置管理机制。
"""

import collections
import gymnasium as gym
import importlib
import inspect
import os
import re
import yaml

from isaaclab.envs import DirectRLEnvCfg, ManagerBasedRLEnvCfg


def load_cfg_from_registry(task_name: str, entry_point_key: str) -> dict | object:
    """从gym注册表中根据入口点加载默认配置。

    此函数从gym注册表中为给定的任务名称加载配置对象。
    它支持YAML和Python配置文件两种格式。

    它期望配置在gym注册表中注册为：

    .. code-block:: python

        gym.register(
            id="My-Awesome-Task-v0",
            ...
            kwargs={"env_entry_point_cfg": "path.to.config:ConfigClass"},
        )


    上述示例的解析配置对象可以通过以下方式获得：

    .. code-block:: python

        from isaaclab_tasks.utils.parse_cfg import load_cfg_from_registry

        cfg = load_cfg_from_registry("My-Awesome-Task-v0", "env_entry_point_cfg")


    Args:
        task_name: 环境的名称。用于在gym注册表中查找对应的任务。
        entry_point_key: 用于解析配置文件的入口点键。

    Returns:
        解析的配置对象。如果入口点是YAML文件，则解析为字典。
        如果入口点是Python类，则实例化并返回。

    Raises:
        ValueError: 如果任务的gym注册表中没有可用的入口点键。
    """
    # 获取配置入口点
    cfg_entry_point = gym.spec(task_name.split(":")[-1]).kwargs.get(entry_point_key)
    # 检查入口点是否存在
    if cfg_entry_point is None:
        # 获取现有的智能体和算法
        agents = collections.defaultdict(list)
        for k in gym.spec(task_name.split(":")[-1]).kwargs:
            if k.endswith("_cfg_entry_point") and k != "env_cfg_entry_point":
                spec = (
                    k.replace("_cfg_entry_point", "")
                    .replace("rl_games", "rl-games")
                    .replace("rsl_rl", "rsl-rl")
                    .split("_")
                )
                agent = spec[0].replace("-", "_")
                algorithms = [item.upper() for item in (spec[1:] if len(spec) > 1 else ["PPO"])]
                agents[agent].extend(algorithms)
        msg = "\n现有的RL库（和算法）配置入口点："
        for agent, algorithms in agents.items():
            msg += f"\n  |-- {agent}: {', '.join(algorithms)}"
        # 抛出错误
        raise ValueError(
            f"无法找到环境的配置：'{task_name}'。"
            f"\n请检查gym注册表是否有入口点：'{entry_point_key}'。"
            f"{msg if agents else ''}"
        )
    # 解析默认配置文件
    if isinstance(cfg_entry_point, str) and cfg_entry_point.endswith(".yaml"):
        if os.path.exists(cfg_entry_point):
            # 配置文件的绝对路径
            config_file = cfg_entry_point
        else:
            # 解析到模块位置的路径
            mod_name, file_name = cfg_entry_point.split(":")
            mod_path = os.path.dirname(importlib.import_module(mod_name).__file__)
            # 获取配置文件路径
            config_file = os.path.join(mod_path, file_name)
        # 加载配置
        print(f"[INFO]: 从以下位置解析配置：{config_file}")
        with open(config_file, encoding="utf-8") as f:
            cfg = yaml.full_load(f)
    else:
        if callable(cfg_entry_point):
            # 解析到模块位置的路径
            mod_path = inspect.getfile(cfg_entry_point)
            # 加载配置
            cfg_cls = cfg_entry_point()
        elif isinstance(cfg_entry_point, str):
            # 解析到模块位置的路径
            mod_name, attr_name = cfg_entry_point.split(":")
            mod = importlib.import_module(mod_name)
            cfg_cls = getattr(mod, attr_name)
        else:
            cfg_cls = cfg_entry_point
        # 加载配置
        print(f"[INFO]: 从以下位置解析配置：{cfg_entry_point}")
        if callable(cfg_cls):
            cfg = cfg_cls()
        else:
            cfg = cfg_cls
    return cfg


def parse_env_cfg(
    task_name: str, device: str = "cuda:0", num_envs: int | None = None, use_fabric: bool | None = None
) -> ManagerBasedRLEnvCfg | DirectRLEnvCfg:
    """解析环境配置并根据输入进行覆盖。

    该函数加载指定任务的默认配置，然后根据提供的参数进行覆盖。
    这允许用户在运行时动态调整环境设置。

    Args:
        task_name: 环境的名称。用于标识特定的环境任务。
        device: 运行仿真的设备。默认为"cuda:0"。
        num_envs: 要创建的环境数量。默认为None，在这种情况下保持不变。
        use_fabric: 是否启用/禁用fabric接口。如果为false，所有读/写操作都通过USD进行。
            这会减慢仿真速度，但允许通过USD阶段查看USD中的更改。
            默认为None，在这种情况下保持不变。

    Returns:
        解析的配置对象。可能是ManagerBasedRLEnvCfg或DirectRLEnvCfg类型。

    Raises:
        RuntimeError: 如果任务的配置不是类。我们假设用户总是为环境配置使用类。
    """
    # 加载默认配置
    cfg = load_cfg_from_registry(task_name.split(":")[-1], "env_cfg_entry_point")

    # 检查它不是字典
    # 我们假设用户总是为配置使用类
    if isinstance(cfg, dict):
        raise RuntimeError(f"任务'{task_name}'的配置不是类。请提供一个类。")

    # 仿真设备
    cfg.sim.device = device
    # 禁用fabric以通过USD读/写
    if use_fabric is not None:
        cfg.sim.use_fabric = use_fabric
    # 环境数量
    if num_envs is not None:
        cfg.scene.num_envs = num_envs

    return cfg


def get_checkpoint_path(
    log_path: str, run_dir: str = ".*", checkpoint: str = ".*", other_dirs: list[str] = None, sort_alpha: bool = True
) -> str:
    """获取输入目录中模型检查点的路径。

    检查点文件解析为：``<log_path>/<run_dir>/<*other_dirs>/<checkpoint>``，其中
    :attr:`other_dirs` 是要连接的中间文件夹名称。这些不能是正则表达式。

    如果 :attr:`run_dir` 和 :attr:`checkpoint` 是正则表达式，则选择最近的（按字母顺序最高）
    运行和检查点。要禁用此行为，请将标志 :attr:`sort_alpha` 设置为False。

    Args:
        log_path: 查找模型的日志目录路径。
        run_dir: 包含运行的目录名称的正则表达式。默认为在 :attr:`log_path` 内创建的最近目录。
        other_dirs: 运行目录和检查点文件之间的中间目录。默认为None，
            这意味着检查点文件直接在运行目录下。
        checkpoint: 模型检查点文件的正则表达式。默认为在 :attr:`run_dir` 目录中保存的最近的torch模型。
        sort_alpha: 是否按字母顺序对运行进行排序。默认为True。
            如果为False，则 :attr:`run_dir` 中的文件夹按最后修改时间排序。

    Returns:
        模型检查点的路径。

    Raises:
        ValueError: 当在输入目录中找不到运行时。
        ValueError: 当在输入目录中找不到检查点时。

    """
    # 检查目录中是否存在运行
    try:
        # 查找目录中匹配正则表达式的所有运行
        runs = [
            os.path.join(log_path, run) for run in os.scandir(log_path) if run.is_dir() and re.match(run_dir, run.name)
        ]
        # 按字母顺序对匹配的运行进行排序（最新的运行应该在最后）
        if sort_alpha:
            runs.sort()
        else:
            runs = sorted(runs, key=os.path.getmtime)
        # 创建最后运行文件路径
        if other_dirs is not None:
            run_path = os.path.join(runs[-1], *other_dirs)
        else:
            run_path = runs[-1]
    except IndexError:
        raise ValueError(f"目录'{log_path}'中没有匹配'{run_dir}'的运行。")

    # 列出目录中的所有模型检查点
    model_checkpoints = [f for f in os.listdir(run_path) if re.match(checkpoint, f)]
    # 检查是否存在任何检查点
    if len(model_checkpoints) == 0:
        raise ValueError(f"目录'{run_path}'中没有匹配'{checkpoint}'的检查点。")
    # 按字母顺序排序，同时确保*_10在*_9之后
    model_checkpoints.sort(key=lambda m: f"{m:0>15}")
    # 获取最新匹配的检查点文件
    checkpoint_file = model_checkpoints[-1]

    return os.path.join(run_path, checkpoint_file)
