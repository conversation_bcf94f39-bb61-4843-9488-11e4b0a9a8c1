# Copyright (c) 2022-2025, The Isaac Lab Project Developers (https://github.com/isaac-sim/IsaacLab/blob/main/CONTRIBUTORS.md).
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

"""首先启动Isaac Sim仿真器。

该测试模块用于测试Isaac Lab中的各种环境。它首先启动Isaac Sim仿真器，
然后运行一系列参数化测试来验证环境的正确性。
"""

import sys

# 在主脚本中导入pinocchio，强制使用IsaacLab安装的依赖项，而不是Isaac Sim安装的依赖项
# Pink IK控制器需要pinocchio
if sys.platform != "win32":
    import pinocchio  # noqa: F401

from isaaclab.app import AppLauncher

# 启动仿真器
app_launcher = AppLauncher(headless=True, enable_cameras=True)
simulation_app = app_launcher.app


"""其余所有内容如下。"""

import pytest
from env_test_utils import _run_environments, setup_environment

import isaaclab_tasks  # noqa: F401


@pytest.mark.parametrize("num_envs, device", [(32, "cuda"), (1, "cuda")])  # 参数化：环境数量和设备
@pytest.mark.parametrize("task_name", setup_environment(include_play=False, factory_envs=False, multi_agent=False))  # 参数化：任务名称
@pytest.mark.isaacsim_ci  # 标记为Isaac Sim CI测试
def test_environments(task_name, num_envs, device):
    """测试环境功能。

    该测试函数验证指定的环境能够正确创建、运行和关闭。
    它使用不同的环境数量和设备配置进行测试。
    """
    # 在不使用内存中阶段的情况下运行环境
    _run_environments(task_name, device, num_envs, create_stage_in_memory=False)
