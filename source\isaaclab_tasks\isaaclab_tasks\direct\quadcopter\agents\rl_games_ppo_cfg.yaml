# Copyright (c) 2022-2025, The Isaac Lab Project Developers (https://github.com/isaac-sim/IsaacLab/blob/main/CONTRIBUTORS.md).
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

# 四旋翼无人机环境的RL Games PPO配置
# 该配置文件定义了使用RL Games库训练四旋翼无人机飞行控制策略的所有参数

params:
  seed: 42  # 随机种子，确保实验可重复性

  # 环境包装器裁剪设置
  env:
    clip_actions: 1.0  # 动作裁剪范围 [-1.0, 1.0]

  # 算法配置
  algo:
    name: a2c_continuous  # 使用连续动作空间的A2C算法

  # 模型配置
  model:
    name: continuous_a2c_logstd  # 连续动作空间的A2C模型，带对数标准差

  # 网络架构配置
  network:
    name: actor_critic  # 演员-评论家网络
    separate: False  # 演员和评论家网络共享参数
    space:
      continuous:  # 连续动作空间配置
        mu_activation: None  # 均值输出不使用激活函数
        sigma_activation: None  # 标准差输出不使用激活函数

        mu_init:  # 均值网络初始化
          name: default
        sigma_init:  # 标准差网络初始化
          name: const_initializer
          val: 0  # 初始化为0（对数标准差）
        fixed_sigma: True  # 使用固定的标准差
    mlp:  # 多层感知机配置
      units: [64, 64]  # 隐藏层单元数：两层64个神经元
      activation: elu  # 激活函数：ELU（适合连续控制）
      d2rl: False  # 不使用D2RL架构

      initializer:  # 权重初始化
        name: default
      regularizer:  # 正则化
        name: None

  # 检查点加载配置
  load_checkpoint: False  # 是否加载检查点
  load_path: ''  # 检查点路径

  # 训练配置
  config:
    name: quadcopter_direct  # 实验名称
    env_name: rlgpu  # 环境名称
    device: 'cuda:0'  # 训练设备
    device_name: 'cuda:0'  # 设备名称
    multi_gpu: False  # 不使用多GPU
    ppo: True  # 使用PPO算法
    mixed_precision: False  # 不使用混合精度训练
    normalize_input: True  # 归一化输入观察
    normalize_value: True  # 归一化价值函数
    value_bootstrap: True  # 使用价值函数自举
    num_actors: -1  # 演员数量（从脚本配置，基于环境数量）

    # 奖励塑形配置
    reward_shaper:
      scale_value: 0.01  # 奖励缩放值

    # PPO算法参数
    normalize_advantage: True  # 归一化优势函数
    gamma: 0.99  # 折扣因子
    tau: 0.95  # GAE lambda参数
    learning_rate: 5e-4  # 学习率
    lr_schedule: adaptive  # 自适应学习率调度
    schedule_type: legacy  # 调度类型
    kl_threshold: 0.016  # KL散度阈值（用于自适应学习率）
    score_to_win: 20000  # 获胜分数阈值
    max_epochs: 200  # 最大训练轮数
    save_best_after: 100  # 在第100轮后开始保存最佳模型
    save_frequency: 25  # 模型保存频率
    grad_norm: 1.0  # 梯度裁剪范数
    entropy_coef: 0.0  # 熵正则化系数
    truncate_grads: True  # 截断梯度
    e_clip: 0.2  # PPO裁剪参数
    horizon_length: 24  # 时间步长度
    minibatch_size: 24576  # 小批次大小
    mini_epochs: 5  # 每次更新的小轮数
    critic_coef: 2  # 评论家损失系数
    clip_value: True  # 裁剪价值函数
    seq_length: 4  # 序列长度
    bounds_loss_coef: 0.0001  # 边界损失系数
