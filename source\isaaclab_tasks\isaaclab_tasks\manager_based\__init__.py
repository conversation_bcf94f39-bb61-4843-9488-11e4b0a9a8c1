# Copyright (c) 2022-2025, The Isaac Lab Project Developers (https://github.com/isaac-sim/IsaacLab/blob/main/CONTRIBUTORS.md).
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

"""
基于配置的工作流环境。

该包包含使用基于管理器的工作流实现的环境。这些环境利用Isaac Lab的
管理器系统，通过配置类来定义环境的各个组件，如场景、观察、动作、
奖励、终止条件等。

基于管理器的工作流适用于：
- 复杂的环境实现
- 需要模块化和可重用组件的场景
- 大规模环境开发
- 需要灵活配置的环境

管理器系统提供了以下优势：
- 模块化设计，便于维护和扩展
- 配置驱动，易于定制和调整
- 代码重用，减少重复开发
- 标准化接口，确保一致性
"""

import gymnasium as gym
