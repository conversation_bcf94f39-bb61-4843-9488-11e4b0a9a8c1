# Copyright (c) 2022-2025, The Isaac Lab Project Developers (https://github.com/isaac-sim/IsaacLab/blob/main/CONTRIBUTORS.md).
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

"""
蚂蚁运动环境。

该模块实现了四足蚂蚁机器人的运动控制任务。蚂蚁机器人需要学会使用
四条腿进行稳定的前进运动，这是一个经典的连续控制和运动学习问题。

环境特点：
- 四足机器人运动控制
- 连续动作空间：关节力矩控制
- 奖励函数：鼓励前进速度，惩罚能耗和不稳定运动
- 终止条件：机器人翻倒或偏离轨道
"""

import gymnasium as gym

from . import agents

##
# 注册Gym环境
##

gym.register(
    id="Isaac-Ant-Direct-v0",
    entry_point=f"{__name__}.ant_env:AntEnv",
    disable_env_checker=True,
    kwargs={
        "env_cfg_entry_point": f"{__name__}.ant_env:AntEnvCfg",  # 环境配置入口点
        "rl_games_cfg_entry_point": f"{agents.__name__}:rl_games_ppo_cfg.yaml",  # RL Games PPO配置
        "rsl_rl_cfg_entry_point": f"{agents.__name__}.rsl_rl_ppo_cfg:AntPPORunnerCfg",  # RSL RL PPO配置
        "skrl_cfg_entry_point": f"{agents.__name__}:skrl_ppo_cfg.yaml",  # SKRL PPO配置
    },
)
