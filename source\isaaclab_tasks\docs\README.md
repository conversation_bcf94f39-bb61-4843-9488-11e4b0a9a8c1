# Isaac Lab: 环境套件

使用作为Isaac Lab一部分开发的核心框架，我们为机器人研究提供各种学习环境。
这些环境遵循OpenAI Gym版本`0.21.0`的`gym.Env` API。环境使用Gym注册表进行注册。

每个环境的名称由`Isaac-<Task>-<Robot>-v<X>`组成，其中`<Task>`表示在环境中要学习的技能，
`<Robot>`表示执行智能体的具体形态，`<X>`表示环境的版本（可用于建议不同的观察或动作空间）。

环境使用Python类（使用`configclass`装饰器包装）或通过YAML文件进行配置。
环境的模板结构总是与环境文件本身放在同一级别。但是，它的各种实例包含在环境目录本身内的目录中。
这看起来如下所示：

```tree
isaaclab_tasks/locomotion/
├── __init__.py
└── velocity
    ├── config
    │   └── anymal_c
    │       ├── agent  # <- 这里存储学习智能体配置
    │       ├── __init__.py  # <- 这里将环境和配置注册到gym注册表
    │       ├── flat_env_cfg.py
    │       └── rough_env_cfg.py
    ├── __init__.py
    └── velocity_env_cfg.py  # <- 这是基础任务配置
```

然后在`isaaclab_tasks/locomotion/velocity/config/anymal_c/__init__.py`中注册环境：

```python
gym.register(
    id="Isaac-Velocity-Rough-Anymal-C-v0",  # 环境ID：Isaac-速度-崎岖地形-Anymal-C机器人-版本0
    entry_point="isaaclab.envs:ManagerBasedRLEnv",  # 入口点：基于管理器的强化学习环境
    disable_env_checker=True,  # 禁用环境检查器
    kwargs={
        "env_cfg_entry_point": f"{__name__}.rough_env_cfg:AnymalCRoughEnvCfg",  # 环境配置入口点
        "rl_games_cfg_entry_point": f"{agents.__name__}:rl_games_rough_ppo_cfg.yaml",  # RL Games配置
        "rsl_rl_cfg_entry_point": f"{agents.__name__}.rsl_rl_ppo_cfg:AnymalCRoughPPORunnerCfg",  # RSL RL配置
        "skrl_cfg_entry_point": f"{agents.__name__}:skrl_rough_ppo_cfg.yaml",  # SKRL配置
    },
)

gym.register(
    id="Isaac-Velocity-Flat-Anymal-C-v0",  # 环境ID：Isaac-速度-平坦地形-Anymal-C机器人-版本0
    entry_point="isaaclab.envs:ManagerBasedRLEnv",  # 入口点：基于管理器的强化学习环境
    disable_env_checker=True,  # 禁用环境检查器
    kwargs={
        "env_cfg_entry_point": f"{__name__}.flat_env_cfg:AnymalCFlatEnvCfg",  # 环境配置入口点
        "rsl_rl_cfg_entry_point": f"{agents.__name__}.rsl_rl_ppo_cfg:AnymalCFlatPPORunnerCfg",  # RSL RL配置
        "rl_games_cfg_entry_point": f"{agents.__name__}:rl_games_flat_ppo_cfg.yaml",  # RL Games配置
        "skrl_cfg_entry_point": f"{agents.__name__}:skrl_flat_ppo_cfg.yaml",  # SKRL配置
    },
)
```

> **注意：** 作为一种实践，我们在单个文件中指定所有环境，以避免不同任务或环境之间的名称冲突。
> 但是，这种做法是有争议的，我们愿意接受建议来处理任务或环境数量的大规模扩展。
