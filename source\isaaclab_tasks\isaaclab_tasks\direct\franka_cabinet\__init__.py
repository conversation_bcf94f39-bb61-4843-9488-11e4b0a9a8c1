# Copyright (c) 2022-2025, The Isaac Lab Project Developers (https://github.com/isaac-sim/IsaacLab/blob/main/CONTRIBUTORS.md).
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

"""
Franka机械臂柜子操作环境。

该模块实现了Franka Emika Panda机械臂操作柜子抽屉的任务。机械臂需要学会
精确地抓取柜子把手并拉开抽屉，这是一个典型的机器人操作和接触控制问题。

环境特点：
- 7自由度Franka机械臂控制
- 精确的抓取和拉拽操作
- 接触力控制和碰撞检测
- 奖励函数：鼓励成功抓取和开启抽屉
- 终止条件：任务完成或操作失败
"""

import gymnasium as gym

from . import agents

##
# 注册Gym环境
##

gym.register(
    id="Isaac-Franka-Cabinet-Direct-v0",
    entry_point=f"{__name__}.franka_cabinet_env:FrankaCabinetEnv",
    disable_env_checker=True,
    kwargs={
        "env_cfg_entry_point": f"{__name__}.franka_cabinet_env:FrankaCabinetEnvCfg",  # 环境配置入口点
        "rl_games_cfg_entry_point": f"{agents.__name__}:rl_games_ppo_cfg.yaml",  # RL Games PPO配置
        "rsl_rl_cfg_entry_point": f"{agents.__name__}.rsl_rl_ppo_cfg:FrankaCabinetPPORunnerCfg",  # RSL RL PPO配置
        "skrl_cfg_entry_point": f"{agents.__name__}:skrl_ppo_cfg.yaml",  # SKRL PPO配置
    },
)
