# Copyright (c) 2022-2025, The Isaac Lab Project Developers (https://github.com/isaac-sim/IsaacLab/blob/main/CONTRIBUTORS.md).
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

"""用于Hydra配置系统的实用工具子模块。

该模块提供了与Hydra配置系统集成的工具，使得Isaac Lab任务能够
使用Hydra进行配置管理和命令行参数解析。Hydra是一个强大的配置
管理框架，支持配置组合、覆盖和动态配置。
"""


import functools
from collections.abc import Callable

try:
    import hydra
    from hydra.core.config_store import ConfigStore
    from omegaconf import DictConfig, OmegaConf
except ImportError:
    raise ImportError("Hydra未安装。请运行'pip install hydra-core'来安装它。")

from isaaclab.envs import DirectRLEnvCfg, ManagerBasedRLEnvCfg
from isaaclab.envs.utils.spaces import replace_env_cfg_spaces_with_strings, replace_strings_with_env_cfg_spaces
from isaaclab.utils import replace_slices_with_strings, replace_strings_with_slices

from isaaclab_tasks.utils.parse_cfg import load_cfg_from_registry


def register_task_to_hydra(
    task_name: str, agent_cfg_entry_point: str
) -> tuple[ManagerBasedRLEnvCfg | DirectRLEnvCfg, dict]:
    """将任务配置注册到Hydra配置存储中。

    此函数根据任务名称解析环境和智能体的配置文件。
    然后将配置注册到Hydra配置存储中，以便后续使用。

    Args:
        task_name: 任务的名称。用于标识特定的环境任务。
        agent_cfg_entry_point: 用于解析智能体配置文件的入口点键。

    Returns:
        包含解析的环境和智能体配置对象的元组。
        第一个元素是环境配置，第二个元素是智能体配置。
    """
    # 加载配置
    env_cfg = load_cfg_from_registry(task_name, "env_cfg_entry_point")
    agent_cfg = None
    if agent_cfg_entry_point:
        agent_cfg = load_cfg_from_registry(task_name, agent_cfg_entry_point)
    # 将gymnasium空间替换为字符串，因为OmegaConf不支持它们。
    # 这必须在将环境配置转换为字典之前完成，以避免内部重新解释
    env_cfg = replace_env_cfg_spaces_with_strings(env_cfg)
    # 将配置转换为字典
    env_cfg_dict = env_cfg.to_dict()
    if isinstance(agent_cfg, dict) or agent_cfg is None:
        agent_cfg_dict = agent_cfg
    else:
        agent_cfg_dict = agent_cfg.to_dict()
    cfg_dict = {"env": env_cfg_dict, "agent": agent_cfg_dict}
    # 将切片替换为字符串，因为OmegaConf不支持切片
    cfg_dict = replace_slices_with_strings(cfg_dict)
    # 将配置存储到Hydra中
    ConfigStore.instance().store(name=task_name, node=cfg_dict)
    return env_cfg, agent_cfg


def hydra_task_config(task_name: str, agent_cfg_entry_point: str) -> Callable:
    """处理任务Hydra配置的装饰器。

    此装饰器将任务注册到Hydra，并从Hydra解析的命令行参数更新环境和智能体配置。
    它简化了使用Hydra进行配置管理的过程，使得用户可以通过命令行轻松覆盖配置参数。

    Args:
        task_name: 任务的名称。用于标识特定的环境任务。
        agent_cfg_entry_point: 用于解析智能体配置文件的入口点键。

    Returns:
        装饰后的函数，其环境和智能体配置已从命令行参数更新。
    """

    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # 将任务注册到Hydra
            env_cfg, agent_cfg = register_task_to_hydra(task_name.split(":")[-1], agent_cfg_entry_point)

            # 定义新的Hydra主函数
            @hydra.main(config_path=None, config_name=task_name.split(":")[-1], version_base="1.3")
            def hydra_main(hydra_env_cfg: DictConfig, env_cfg=env_cfg, agent_cfg=agent_cfg):
                # 转换为原生字典
                hydra_env_cfg = OmegaConf.to_container(hydra_env_cfg, resolve=True)
                # 将字符串替换为切片，因为OmegaConf不支持切片
                hydra_env_cfg = replace_strings_with_slices(hydra_env_cfg)
                # 使用Hydra命令行参数更新配置
                env_cfg.from_dict(hydra_env_cfg["env"])
                # 替换表示gymnasium空间的字符串，因为OmegaConf不支持它们。
                # 这必须在从字典转换环境配置后完成，以避免内部重新解释
                env_cfg = replace_strings_with_env_cfg_spaces(env_cfg)
                # 获取智能体配置
                if isinstance(agent_cfg, dict) or agent_cfg is None:
                    agent_cfg = hydra_env_cfg["agent"]
                else:
                    agent_cfg.from_dict(hydra_env_cfg["agent"])
                # 调用原始函数
                func(env_cfg, agent_cfg, *args, **kwargs)

            # 调用新的Hydra主函数
            hydra_main()

        return wrapper

    return decorator
