# Copyright (c) 2022-2025, The Isaac Lab Project Developers (https://github.com/isaac-sim/IsaacLab/blob/main/CONTRIBUTORS.md).
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

"""
四旋翼无人机飞行控制环境实现。

该模块实现了四旋翼无人机的飞行控制任务，基于Crazyflie无人机模型。
无人机需要学会通过控制四个螺旋桨的推力来实现稳定飞行、悬停和
目标位置跟踪等复杂的空中机动。

环境特点：
- 基于物理的四旋翼无人机动力学模型
- 连续动作空间：总推力和三轴力矩控制
- 12维观察空间：线速度、角速度、重力投影、目标位置
- 奖励函数：鼓励到达目标位置，惩罚过度运动
- 终止条件：无人机坠毁或飞出安全区域
- 可视化调试：目标位置标记和轨迹显示

技术要点：
- 推力-重量比控制：可调节的推力输出范围
- 力矩缩放：精确的姿态控制参数
- 目标跟踪：随机生成的3D目标位置
- 物理仿真：真实的空气动力学和重力效应
"""

from __future__ import annotations

import gymnasium as gym
import torch

import isaaclab.sim as sim_utils
from isaaclab.assets import Articulation, ArticulationCfg
from isaaclab.envs import DirectRLEnv, DirectRLEnvCfg
from isaaclab.envs.ui import BaseEnvWindow
from isaaclab.markers import VisualizationMarkers
from isaaclab.scene import InteractiveSceneCfg
from isaaclab.sim import SimulationCfg
from isaaclab.terrains import TerrainImporterCfg
from isaaclab.utils import configclass
from isaaclab.utils.math import subtract_frame_transforms

##
# 预定义配置
##
from isaaclab_assets import CRAZYFLIE_CFG  # isort: skip
from isaaclab.markers import CUBOID_MARKER_CFG  # isort: skip


class QuadcopterEnvWindow(BaseEnvWindow):
    """四旋翼无人机环境的窗口管理器。

    该类扩展了基础环境窗口，为四旋翼无人机环境添加了专门的UI元素，
    包括目标位置的可视化控制和调试信息显示。
    """

    def __init__(self, env: QuadcopterEnv, window_name: str = "IsaacLab"):
        """初始化窗口。

        Args:
            env: 四旋翼无人机环境对象
            window_name: 窗口名称，默认为"IsaacLab"
        """
        # 初始化基础窗口
        super().__init__(env, window_name)
        # 添加自定义UI元素
        with self.ui_window_elements["main_vstack"]:
            with self.ui_window_elements["debug_frame"]:
                with self.ui_window_elements["debug_vstack"]:
                    # 添加目标位置可视化控制
                    self._create_debug_vis_ui_element("targets", self.env)


@configclass
class QuadcopterEnvCfg(DirectRLEnvCfg):
    """四旋翼无人机环境配置类。

    该配置类定义了四旋翼无人机飞行控制环境的所有参数，包括仿真设置、
    机器人配置、场景设置、物理参数和奖励权重等。
    """

    # 环境参数
    episode_length_s = 10.0  # 每个回合的最大时长（秒）
    decimation = 2  # 控制频率降采样因子（仿真步数/控制步数）
    action_space = 4  # 动作空间维度：推力(1) + 三轴力矩(3)
    observation_space = 12  # 观察空间维度：线速度(3) + 角速度(3) + 重力投影(3) + 目标位置(3)
    state_space = 0  # 状态空间维度（此环境中未使用）
    debug_vis = True  # 是否启用调试可视化

    ui_window_class_type = QuadcopterEnvWindow  # UI窗口类型

    # 仿真配置
    sim: SimulationCfg = SimulationCfg(
        dt=1 / 100,  # 仿真时间步长：10ms
        render_interval=decimation,  # 渲染间隔
        physics_material=sim_utils.RigidBodyMaterialCfg(
            friction_combine_mode="multiply",  # 摩擦力组合模式
            restitution_combine_mode="multiply",  # 恢复系数组合模式
            static_friction=1.0,  # 静摩擦系数
            dynamic_friction=1.0,  # 动摩擦系数
            restitution=0.0,  # 恢复系数（完全非弹性碰撞）
        ),
    )

    # 地形配置
    terrain = TerrainImporterCfg(
        prim_path="/World/ground",  # 地面路径
        terrain_type="plane",  # 地形类型：平面
        collision_group=-1,  # 碰撞组
        physics_material=sim_utils.RigidBodyMaterialCfg(
            friction_combine_mode="multiply",
            restitution_combine_mode="multiply",
            static_friction=1.0,
            dynamic_friction=1.0,
            restitution=0.0,
        ),
        debug_vis=False,  # 不显示地形调试信息
    )

    # 场景配置
    scene: InteractiveSceneCfg = InteractiveSceneCfg(
        num_envs=4096,  # 并行环境数量
        env_spacing=2.5,  # 环境间距（米）
        replicate_physics=True,  # 复制物理设置
        clone_in_fabric=True  # 在Fabric中克隆
    )

    # 机器人配置
    robot: ArticulationCfg = CRAZYFLIE_CFG.replace(prim_path="/World/envs/env_.*/Robot")
    thrust_to_weight = 1.9  # 推力重量比：最大推力/无人机重量
    moment_scale = 0.01  # 力矩缩放因子：控制姿态响应灵敏度

    # 奖励权重
    lin_vel_reward_scale = -0.05  # 线速度惩罚权重（鼓励平稳飞行）
    ang_vel_reward_scale = -0.01  # 角速度惩罚权重（鼓励稳定姿态）
    distance_to_goal_reward_scale = 15.0  # 目标距离奖励权重（鼓励接近目标）


class QuadcopterEnv(DirectRLEnv):
    """四旋翼无人机飞行控制环境类。

    该类实现了基于Crazyflie无人机模型的飞行控制任务。无人机需要学会
    通过控制总推力和三轴力矩来实现稳定飞行和目标位置跟踪。

    环境动力学：
    - 推力控制：垂直方向的总推力，用于高度和垂直运动控制
    - 力矩控制：绕三个轴的力矩，用于姿态和水平运动控制
    - 重力效应：考虑重力对飞行动力学的影响
    - 空气阻力：通过速度惩罚间接模拟空气阻力效应

    Attributes:
        cfg: 环境配置
        _actions: 当前动作（推力和力矩）
        _thrust: 施加到无人机的推力向量
        _moment: 施加到无人机的力矩向量
        _desired_pos_w: 世界坐标系下的目标位置
        _episode_sums: 回合奖励累计统计
        _body_id: 无人机主体的身体ID
        _robot_mass: 无人机质量
        _robot_weight: 无人机重量（质量×重力加速度）
    """
    cfg: QuadcopterEnvCfg

    def __init__(self, cfg: QuadcopterEnvCfg, render_mode: str | None = None, **kwargs):
        """初始化四旋翼无人机环境。

        Args:
            cfg: 环境配置
            render_mode: 渲染模式
            **kwargs: 其他关键字参数
        """
        super().__init__(cfg, render_mode, **kwargs)

        # 施加到无人机基座的总推力和力矩
        self._actions = torch.zeros(self.num_envs, gym.spaces.flatdim(self.single_action_space), device=self.device)
        self._thrust = torch.zeros(self.num_envs, 1, 3, device=self.device)  # 推力向量（仅Z轴有效）
        self._moment = torch.zeros(self.num_envs, 1, 3, device=self.device)  # 三轴力矩向量

        # 目标位置（世界坐标系）
        self._desired_pos_w = torch.zeros(self.num_envs, 3, device=self.device)

        # 回合统计日志
        self._episode_sums = {
            key: torch.zeros(self.num_envs, dtype=torch.float, device=self.device)
            for key in [
                "lin_vel",  # 线速度惩罚累计
                "ang_vel",  # 角速度惩罚累计
                "distance_to_goal",  # 目标距离奖励累计
            ]
        }

        # 获取无人机身体索引和物理参数
        self._body_id = self._robot.find_bodies("body")[0]  # 主体身体ID
        self._robot_mass = self._robot.root_physx_view.get_masses()[0].sum()  # 无人机总质量
        self._gravity_magnitude = torch.tensor(self.sim.cfg.gravity, device=self.device).norm()  # 重力加速度大小
        self._robot_weight = (self._robot_mass * self._gravity_magnitude).item()  # 无人机重量

        # 设置调试可视化（在set_debug_vis内部设置为有效句柄）
        self.set_debug_vis(self.cfg.debug_vis)

    def _setup_scene(self):
        """设置仿真场景。

        创建无人机机器人、地形、光照等场景元素，并配置环境克隆。
        """
        # 创建无人机机器人
        self._robot = Articulation(self.cfg.robot)
        self.scene.articulations["robot"] = self._robot

        # 配置地形
        self.cfg.terrain.num_envs = self.scene.cfg.num_envs
        self.cfg.terrain.env_spacing = self.scene.cfg.env_spacing
        self._terrain = self.cfg.terrain.class_type(self.cfg.terrain)

        # 克隆和复制环境
        self.scene.clone_environments(copy_from_source=False)

        # CPU仿真需要显式过滤碰撞
        if self.device == "cpu":
            self.scene.filter_collisions(global_prim_paths=[self.cfg.terrain.prim_path])

        # 添加光照
        light_cfg = sim_utils.DomeLightCfg(intensity=2000.0, color=(0.75, 0.75, 0.75))
        light_cfg.func("/World/Light", light_cfg)

    def _pre_physics_step(self, actions: torch.Tensor):
        """物理步骤前的预处理。

        将归一化的动作转换为实际的推力和力矩值。

        动作映射：
        - actions[0]: 推力控制 [-1, 1] -> [0, 2*thrust_to_weight*weight]
        - actions[1:4]: 力矩控制 [-1, 1] -> [-moment_scale, moment_scale]

        Args:
            actions: 归一化的动作张量，形状为 (num_envs, 4)
                    [推力, roll力矩, pitch力矩, yaw力矩]
        """
        # 限制动作范围并缓存
        self._actions = actions.clone().clamp(-1.0, 1.0)

        # 推力映射：从[-1,1]映射到[0, 2*推力重量比*重量]
        # 这样可以实现从0推力到最大推力的控制
        self._thrust[:, 0, 2] = self.cfg.thrust_to_weight * self._robot_weight * (self._actions[:, 0] + 1.0) / 2.0

        # 力矩映射：直接缩放到合适的力矩范围
        self._moment[:, 0, :] = self.cfg.moment_scale * self._actions[:, 1:]

    def _apply_action(self):
        """应用动作到仿真中。

        将计算得到的推力和力矩施加到无人机的主体上。
        """
        self._robot.set_external_force_and_torque(self._thrust, self._moment, body_ids=self._body_id)

    def _get_observations(self) -> dict:
        """获取环境观察。

        观察空间包含12个维度：
        - 线速度 (3D): 无人机在机体坐标系下的线速度
        - 角速度 (3D): 无人机在机体坐标系下的角速度
        - 重力投影 (3D): 重力在机体坐标系下的投影（用于姿态感知）
        - 目标位置 (3D): 目标位置在机体坐标系下的相对位置

        Returns:
            包含策略观察的字典，观察向量形状为 (num_envs, 12)
        """
        # 将目标位置从世界坐标系转换到机体坐标系
        desired_pos_b, _ = subtract_frame_transforms(
            self._robot.data.root_pos_w, self._robot.data.root_quat_w, self._desired_pos_w
        )

        # 拼接所有观察分量
        obs = torch.cat(
            [
                self._robot.data.root_lin_vel_b,      # 机体坐标系线速度 (3D)
                self._robot.data.root_ang_vel_b,      # 机体坐标系角速度 (3D)
                self._robot.data.projected_gravity_b,  # 重力在机体坐标系的投影 (3D)
                desired_pos_b,                        # 目标位置在机体坐标系 (3D)
            ],
            dim=-1,
        )
        observations = {"policy": obs}
        return observations

    def _get_rewards(self) -> torch.Tensor:
        """计算奖励。

        奖励函数包含三个组成部分：
        1. 线速度惩罚：鼓励平稳飞行，避免过度加速
        2. 角速度惩罚：鼓励稳定姿态，避免过度旋转
        3. 目标距离奖励：鼓励接近目标位置

        距离奖励使用tanh映射，使得：
        - 距离为0时奖励最大(1.0)
        - 距离增加时奖励平滑递减
        - 距离很大时奖励趋近于0

        Returns:
            每个环境的总奖励，形状为 (num_envs,)
        """
        # 计算速度的平方和（能量惩罚）
        lin_vel = torch.sum(torch.square(self._robot.data.root_lin_vel_b), dim=1)
        ang_vel = torch.sum(torch.square(self._robot.data.root_ang_vel_b), dim=1)

        # 计算到目标的距离
        distance_to_goal = torch.linalg.norm(self._desired_pos_w - self._robot.data.root_pos_w, dim=1)

        # 使用tanh函数将距离映射到[0,1]范围的奖励
        # 0.8是缩放参数，控制奖励衰减的速度
        distance_to_goal_mapped = 1 - torch.tanh(distance_to_goal / 0.8)

        # 计算各项奖励分量
        rewards = {
            "lin_vel": lin_vel * self.cfg.lin_vel_reward_scale * self.step_dt,
            "ang_vel": ang_vel * self.cfg.ang_vel_reward_scale * self.step_dt,
            "distance_to_goal": distance_to_goal_mapped * self.cfg.distance_to_goal_reward_scale * self.step_dt,
        }

        # 总奖励
        reward = torch.sum(torch.stack(list(rewards.values())), dim=0)

        # 记录统计信息
        for key, value in rewards.items():
            self._episode_sums[key] += value
        return reward

    def _get_dones(self) -> tuple[torch.Tensor, torch.Tensor]:
        """检查环境终止条件。

        环境在以下情况下终止：
        1. 无人机高度过低（< 0.1m）：坠毁
        2. 无人机高度过高（> 2.0m）：飞出安全区域
        3. 达到最大回合长度：超时

        Returns:
            terminated: 因失败而终止的环境掩码
            time_out: 因超时而终止的环境掩码
        """
        # 检查超时
        time_out = self.episode_length_buf >= self.max_episode_length - 1

        # 检查高度越界（坠毁或飞得太高）
        died = torch.logical_or(
            self._robot.data.root_pos_w[:, 2] < 0.1,   # 高度过低
            self._robot.data.root_pos_w[:, 2] > 2.0    # 高度过高
        )

        return died, time_out

    def _reset_idx(self, env_ids: torch.Tensor | None):
        """重置指定的环境。

        重置包括以下步骤：
        1. 记录回合统计信息
        2. 重置无人机状态到初始位置
        3. 随机生成新的目标位置
        4. 清零动作和累计奖励

        Args:
            env_ids: 要重置的环境索引，如果为None则重置所有环境
        """
        if env_ids is None or len(env_ids) == self.num_envs:
            env_ids = self._robot._ALL_INDICES

        # 记录回合结束时的统计信息
        final_distance_to_goal = torch.linalg.norm(
            self._desired_pos_w[env_ids] - self._robot.data.root_pos_w[env_ids], dim=1
        ).mean()

        # 记录奖励统计
        extras = dict()
        for key in self._episode_sums.keys():
            episodic_sum_avg = torch.mean(self._episode_sums[key][env_ids])
            extras["Episode_Reward/" + key] = episodic_sum_avg / self.max_episode_length_s
            self._episode_sums[key][env_ids] = 0.0  # 重置累计奖励

        self.extras["log"] = dict()
        self.extras["log"].update(extras)

        # 记录终止原因统计
        extras = dict()
        extras["Episode_Termination/died"] = torch.count_nonzero(self.reset_terminated[env_ids]).item()
        extras["Episode_Termination/time_out"] = torch.count_nonzero(self.reset_time_outs[env_ids]).item()
        extras["Metrics/final_distance_to_goal"] = final_distance_to_goal.item()
        self.extras["log"].update(extras)

        # 重置机器人
        self._robot.reset(env_ids)
        super()._reset_idx(env_ids)

        # 如果重置所有环境，分散重置时间以避免训练中的峰值
        if len(env_ids) == self.num_envs:
            self.episode_length_buf = torch.randint_like(
                self.episode_length_buf, high=int(self.max_episode_length)
            )

        # 重置动作
        self._actions[env_ids] = 0.0

        # 随机生成新的目标位置
        # X-Y平面：在[-2, 2]米范围内随机
        self._desired_pos_w[env_ids, :2] = torch.zeros_like(self._desired_pos_w[env_ids, :2]).uniform_(-2.0, 2.0)
        self._desired_pos_w[env_ids, :2] += self._terrain.env_origins[env_ids, :2]  # 加上环境原点偏移
        # Z轴：在[0.5, 1.5]米高度范围内随机
        self._desired_pos_w[env_ids, 2] = torch.zeros_like(self._desired_pos_w[env_ids, 2]).uniform_(0.5, 1.5)

        # 重置机器人状态到初始位置
        joint_pos = self._robot.data.default_joint_pos[env_ids]
        joint_vel = self._robot.data.default_joint_vel[env_ids]
        default_root_state = self._robot.data.default_root_state[env_ids]
        default_root_state[:, :3] += self._terrain.env_origins[env_ids]  # 加上环境原点偏移

        # 将状态写入仿真
        self._robot.write_root_pose_to_sim(default_root_state[:, :7], env_ids)
        self._robot.write_root_velocity_to_sim(default_root_state[:, 7:], env_ids)
        self._robot.write_joint_state_to_sim(joint_pos, joint_vel, None, env_ids)

    def _set_debug_vis_impl(self, debug_vis: bool):
        """设置调试可视化的实现。

        创建或控制目标位置标记的可视化显示。

        Args:
            debug_vis: 是否启用调试可视化
        """
        # 如果需要，首次创建标记
        if debug_vis:
            if not hasattr(self, "goal_pos_visualizer"):
                # 配置目标位置标记
                marker_cfg = CUBOID_MARKER_CFG.copy()
                marker_cfg.markers["cuboid"].size = (0.05, 0.05, 0.05)  # 5cm的小立方体
                marker_cfg.prim_path = "/Visuals/Command/goal_position"  # 目标位置标记路径
                self.goal_pos_visualizer = VisualizationMarkers(marker_cfg)
            # 设置可见性为真
            self.goal_pos_visualizer.set_visibility(True)
        else:
            # 隐藏标记
            if hasattr(self, "goal_pos_visualizer"):
                self.goal_pos_visualizer.set_visibility(False)

    def _debug_vis_callback(self, event):
        """调试可视化回调函数。

        更新目标位置标记的显示位置。

        Args:
            event: 回调事件（未使用）
        """
        # 更新目标位置标记
        self.goal_pos_visualizer.visualize(self._desired_pos_w)
