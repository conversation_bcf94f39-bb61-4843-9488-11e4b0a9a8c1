# Copyright (c) 2022-2025, The Isaac Lab Project Developers (https://github.com/isaac-sim/IsaacLab/blob/main/CONTRIBUTORS.md).
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

"""包含各种机器人环境任务实现的包。

该包的结构如下：

- ``direct``: 包含任务的单文件实现。这些是直接的环境实现，不依赖于管理器API。
- ``manager_based``: 包含使用基于管理器API的任务实现。这些实现使用Isaac Lab的管理器系统。
- ``utils``: 包含任务的实用工具函数。提供配置解析、导入和其他辅助功能。

该包为Isaac Lab提供了丰富的机器人学习任务，涵盖了从基础控制到复杂操作的各种场景。
"""

import os
import toml

# 通过相对路径方便访问其他模块目录
ISAACLAB_TASKS_EXT_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), "../"))
"""扩展源目录的路径。指向isaaclab_tasks包的根目录。"""

ISAACLAB_TASKS_METADATA = toml.load(os.path.join(ISAACLAB_TASKS_EXT_DIR, "config", "extension.toml"))
"""从extension.toml文件解析的扩展元数据字典。包含包的版本、描述等信息。"""

# 配置模块级变量
__version__ = ISAACLAB_TASKS_METADATA["package"]["version"]

##
# 注册Gym环境。
##

from .utils import import_packages

# 黑名单用于防止从子包导入配置
# TODO(@ashwinvk): 一旦Isaac Sim的pinocchio兼容性问题解决，从黑名单中移除pick_place
_BLACKLIST_PKGS = ["utils", ".mdp", "pick_place"]
# 导入此包中的所有配置
import_packages(__name__, _BLACKLIST_PKGS)
