# Copyright (c) 2022-2025, The Isaac Lab Project Developers (https://github.com/isaac-sim/IsaacLab/blob/main/CONTRIBUTORS.md).
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

"""
基于相机观察的倒立摆平衡环境实现。

该模块实现了使用相机图像作为观察输入的倒立摆控制任务。与基础版本不同，
这个环境使用RGB或深度图像而不是状态向量作为智能体的观察输入，
展示了视觉强化学习的应用。

环境特点：
- 视觉观察：RGB图像或深度图像
- 连续动作空间：对小车施加力
- 相机配置：可调节位置、分辨率和数据类型
- 图像预处理：归一化和无效值处理
"""

from __future__ import annotations

import math
import torch
from collections.abc import Sequence

from isaaclab_assets.robots.cartpole import CARTPOLE_CFG

import isaaclab.sim as sim_utils
from isaaclab.assets import Articulation, ArticulationCfg
from isaaclab.envs import DirectRLEnv, DirectRLEnvCfg, ViewerCfg
from isaaclab.scene import InteractiveSceneCfg
from isaaclab.sensors import TiledCamera, TiledCameraCfg, save_images_to_file
from isaaclab.sim import SimulationCfg
from isaaclab.utils import configclass
from isaaclab.utils.math import sample_uniform


@configclass
class CartpoleRGBCameraEnvCfg(DirectRLEnvCfg):
    """RGB相机倒立摆环境配置类。

    该配置类定义了使用RGB相机作为观察输入的倒立摆环境参数。
    相机提供100x100像素的RGB图像作为智能体的观察输入。
    """

    # 环境参数
    decimation = 2  # 控制频率降采样因子
    episode_length_s = 5.0  # 每个回合的最大时长（秒）
    action_scale = 100.0  # 动作缩放因子 [N]

    # 仿真配置
    sim: SimulationCfg = SimulationCfg(dt=1 / 120, render_interval=decimation)

    # 机器人配置
    robot_cfg: ArticulationCfg = CARTPOLE_CFG.replace(prim_path="/World/envs/env_.*/Robot")
    cart_dof_name = "slider_to_cart"  # 小车关节名称
    pole_dof_name = "cart_to_pole"  # 杆子关节名称

    # 相机配置
    tiled_camera: TiledCameraCfg = TiledCameraCfg(
        prim_path="/World/envs/env_.*/Camera",  # 相机路径
        offset=TiledCameraCfg.OffsetCfg(
            pos=(-5.0, 0.0, 2.0),  # 相机位置：距离倒立摆5米，高度2米
            rot=(1.0, 0.0, 0.0, 0.0),  # 相机旋转（四元数）
            convention="world"  # 世界坐标系
        ),
        data_types=["rgb"],  # 数据类型：RGB图像
        spawn=sim_utils.PinholeCameraCfg(
            focal_length=24.0,  # 焦距
            focus_distance=400.0,  # 对焦距离
            horizontal_aperture=20.955,  # 水平光圈
            clipping_range=(0.1, 20.0)  # 裁剪范围
        ),
        width=100,  # 图像宽度
        height=100,  # 图像高度
    )
    write_image_to_file = False  # 是否将图像保存到文件

    # 空间配置
    action_space = 1  # 动作空间维度
    state_space = 0  # 状态空间维度（未使用）
    observation_space = [tiled_camera.height, tiled_camera.width, 3]  # 观察空间：100x100x3 RGB图像

    # 修改查看器设置
    viewer = ViewerCfg(eye=(20.0, 20.0, 20.0))  # 查看器相机位置

    # 场景配置（相机环境使用较少的环境数量和更大的间距）
    scene: InteractiveSceneCfg = InteractiveSceneCfg(num_envs=512, env_spacing=20.0, replicate_physics=True)

    # 重置参数
    max_cart_pos = 3.0  # 小车超出此位置时重置环境 [m]
    initial_pole_angle_range = [-0.125, 0.125]  # 重置时杆子角度的采样范围 [rad]（比基础版本更小）

    # 奖励权重
    rew_scale_alive = 1.0  # 存活奖励权重
    rew_scale_terminated = -2.0  # 终止惩罚权重
    rew_scale_pole_pos = -1.0  # 杆子位置惩罚权重
    rew_scale_cart_vel = -0.01  # 小车速度惩罚权重
    rew_scale_pole_vel = -0.005  # 杆子速度惩罚权重


@configclass
class CartpoleDepthCameraEnvCfg(CartpoleRGBCameraEnvCfg):
    """深度相机倒立摆环境配置类。

    该配置类继承自RGB相机配置，但使用深度图像而不是RGB图像。
    深度图像提供场景的距离信息，可能对某些学习任务更有效。
    """

    # 相机配置（覆盖父类配置以使用深度数据）
    tiled_camera: TiledCameraCfg = TiledCameraCfg(
        prim_path="/World/envs/env_.*/Camera",  # 相机路径
        offset=TiledCameraCfg.OffsetCfg(
            pos=(-5.0, 0.0, 2.0),  # 相机位置
            rot=(1.0, 0.0, 0.0, 0.0),  # 相机旋转
            convention="world"  # 世界坐标系
        ),
        data_types=["depth"],  # 数据类型：深度图像
        spawn=sim_utils.PinholeCameraCfg(
            focal_length=24.0,  # 焦距
            focus_distance=400.0,  # 对焦距离
            horizontal_aperture=20.955,  # 水平光圈
            clipping_range=(0.1, 20.0)  # 裁剪范围
        ),
        width=100,  # 图像宽度
        height=100,  # 图像高度
    )

    # 空间配置（深度图像只有1个通道）
    observation_space = [tiled_camera.height, tiled_camera.width, 1]  # 观察空间：100x100x1 深度图像


class CartpoleCameraEnv(DirectRLEnv):
    """基于相机观察的倒立摆环境类。

    该类实现了使用相机图像（RGB或深度）作为观察输入的倒立摆平衡任务。
    与基础版本不同，这个环境使用视觉信息而不是状态向量来训练智能体。

    Attributes:
        cfg: 环境配置（RGB或深度相机配置）
        _cart_dof_idx: 小车关节索引
        _pole_dof_idx: 杆子关节索引
        action_scale: 动作缩放因子
        joint_pos: 关节位置
        joint_vel: 关节速度
        _cartpole: 倒立摆机器人
        _tiled_camera: 平铺相机传感器
    """

    cfg: CartpoleRGBCameraEnvCfg | CartpoleDepthCameraEnvCfg

    def __init__(
        self, cfg: CartpoleRGBCameraEnvCfg | CartpoleDepthCameraEnvCfg, render_mode: str | None = None, **kwargs
    ):
        """初始化相机倒立摆环境。

        Args:
            cfg: 环境配置（RGB或深度相机配置）
            render_mode: 渲染模式
            **kwargs: 其他关键字参数
        """
        super().__init__(cfg, render_mode, **kwargs)

        # 获取关节索引
        self._cart_dof_idx, _ = self._cartpole.find_joints(self.cfg.cart_dof_name)
        self._pole_dof_idx, _ = self._cartpole.find_joints(self.cfg.pole_dof_name)
        self.action_scale = self.cfg.action_scale

        # 缓存关节状态引用以提高性能
        self.joint_pos = self._cartpole.data.joint_pos
        self.joint_vel = self._cartpole.data.joint_vel

        # 验证相机配置（只支持单一图像类型）
        if len(self.cfg.tiled_camera.data_types) != 1:
            raise ValueError(
                "倒立摆相机环境一次只支持一种图像类型，但提供了以下类型："
                f" {self.cfg.tiled_camera.data_types}"
            )

    def close(self):
        """环境清理。"""
        super().close()

    def _setup_scene(self):
        """设置包含倒立摆和相机的仿真场景。

        创建倒立摆机器人、相机传感器和光照等场景元素。
        """
        # 创建倒立摆机器人和相机
        self._cartpole = Articulation(self.cfg.robot_cfg)
        self._tiled_camera = TiledCamera(self.cfg.tiled_camera)

        # 克隆和复制环境
        self.scene.clone_environments(copy_from_source=False)
        if self.device == "cpu":
            # CPU仿真需要显式过滤碰撞
            self.scene.filter_collisions(global_prim_paths=[])

        # 将关节机器人和传感器添加到场景
        self.scene.articulations["cartpole"] = self._cartpole
        self.scene.sensors["tiled_camera"] = self._tiled_camera

        # 添加光照（对相机观察很重要）
        light_cfg = sim_utils.DomeLightCfg(intensity=2000.0, color=(0.75, 0.75, 0.75))
        light_cfg.func("/World/Light", light_cfg)

    def _pre_physics_step(self, actions: torch.Tensor) -> None:
        """物理步骤前的预处理。

        Args:
            actions: 归一化的动作张量
        """
        self.actions = self.action_scale * actions.clone()

    def _apply_action(self) -> None:
        """应用动作到仿真中。"""
        self._cartpole.set_joint_effort_target(self.actions, joint_ids=self._cart_dof_idx)

    def _get_observations(self) -> dict:
        """获取相机观察。

        根据配置返回RGB或深度图像观察。对图像进行适当的预处理：
        - RGB图像：归一化到[0,1]范围并减去均值
        - 深度图像：将无穷大值设为0

        Returns:
            包含相机图像观察的字典
        """
        # 确定数据类型
        data_type = "rgb" if "rgb" in self.cfg.tiled_camera.data_types else "depth"

        if "rgb" in self.cfg.tiled_camera.data_types:
            # RGB图像预处理
            camera_data = self._tiled_camera.data.output[data_type] / 255.0  # 归一化到[0,1]
            # 归一化相机数据以获得更好的训练结果
            mean_tensor = torch.mean(camera_data, dim=(1, 2), keepdim=True)
            camera_data -= mean_tensor
        elif "depth" in self.cfg.tiled_camera.data_types:
            # 深度图像预处理
            camera_data = self._tiled_camera.data.output[data_type]
            camera_data[camera_data == float("inf")] = 0  # 将无穷大深度值设为0

        observations = {"policy": camera_data.clone()}

        # 可选：保存图像到文件用于调试
        if self.cfg.write_image_to_file:
            save_images_to_file(observations["policy"], f"cartpole_{data_type}.png")

        return observations

    def _get_rewards(self) -> torch.Tensor:
        """计算奖励（与基础倒立摆环境相同）。

        Returns:
            每个环境的总奖励
        """
        total_reward = compute_rewards(
            self.cfg.rew_scale_alive,
            self.cfg.rew_scale_terminated,
            self.cfg.rew_scale_pole_pos,
            self.cfg.rew_scale_cart_vel,
            self.cfg.rew_scale_pole_vel,
            self.joint_pos[:, self._pole_dof_idx[0]],
            self.joint_vel[:, self._pole_dof_idx[0]],
            self.joint_pos[:, self._cart_dof_idx[0]],
            self.joint_vel[:, self._cart_dof_idx[0]],
            self.reset_terminated,
        )
        return total_reward

    def _get_dones(self) -> tuple[torch.Tensor, torch.Tensor]:
        """检查环境终止条件（与基础倒立摆环境相同）。

        Returns:
            terminated: 因失败而终止的环境掩码
            time_out: 因超时而终止的环境掩码
        """
        # 更新关节状态
        self.joint_pos = self._cartpole.data.joint_pos
        self.joint_vel = self._cartpole.data.joint_vel

        # 检查终止条件
        time_out = self.episode_length_buf >= self.max_episode_length - 1
        out_of_bounds = torch.any(torch.abs(self.joint_pos[:, self._cart_dof_idx]) > self.cfg.max_cart_pos, dim=1)
        out_of_bounds = out_of_bounds | torch.any(torch.abs(self.joint_pos[:, self._pole_dof_idx]) > math.pi / 2, dim=1)
        return out_of_bounds, time_out

    def _reset_idx(self, env_ids: Sequence[int] | None):
        """重置指定的环境（与基础倒立摆环境相同）。

        Args:
            env_ids: 要重置的环境索引
        """
        if env_ids is None:
            env_ids = self._cartpole._ALL_INDICES
        super()._reset_idx(env_ids)

        # 设置初始关节位置（随机化杆子角度）
        joint_pos = self._cartpole.data.default_joint_pos[env_ids]
        joint_pos[:, self._pole_dof_idx] += sample_uniform(
            self.cfg.initial_pole_angle_range[0] * math.pi,
            self.cfg.initial_pole_angle_range[1] * math.pi,
            joint_pos[:, self._pole_dof_idx].shape,
            joint_pos.device,
        )
        joint_vel = self._cartpole.data.default_joint_vel[env_ids]

        # 设置根状态
        default_root_state = self._cartpole.data.default_root_state[env_ids]
        default_root_state[:, :3] += self.scene.env_origins[env_ids]

        # 更新缓存的关节状态
        self.joint_pos[env_ids] = joint_pos
        self.joint_vel[env_ids] = joint_vel

        # 将状态写入仿真
        self._cartpole.write_root_pose_to_sim(default_root_state[:, :7], env_ids)
        self._cartpole.write_root_velocity_to_sim(default_root_state[:, 7:], env_ids)
        self._cartpole.write_joint_state_to_sim(joint_pos, joint_vel, None, env_ids)


@torch.jit.script
def compute_rewards(
    rew_scale_alive: float,
    rew_scale_terminated: float,
    rew_scale_pole_pos: float,
    rew_scale_cart_vel: float,
    rew_scale_pole_vel: float,
    pole_pos: torch.Tensor,
    pole_vel: torch.Tensor,
    cart_pos: torch.Tensor,
    cart_vel: torch.Tensor,
    reset_terminated: torch.Tensor,
):
    """计算相机倒立摆环境的奖励（与基础版本相同）。

    该函数与基础倒立摆环境的奖励计算完全相同，因为奖励仍然基于
    物理状态而不是视觉观察。

    Args:
        rew_scale_alive: 存活奖励的缩放因子
        rew_scale_terminated: 终止惩罚的缩放因子
        rew_scale_pole_pos: 杆子位置惩罚的缩放因子
        rew_scale_cart_vel: 小车速度惩罚的缩放因子
        rew_scale_pole_vel: 杆子速度惩罚的缩放因子
        pole_pos: 杆子角度位置
        pole_vel: 杆子角速度
        cart_pos: 小车位置
        cart_vel: 小车速度
        reset_terminated: 环境终止掩码

    Returns:
        每个环境的总奖励
    """
    rew_alive = rew_scale_alive * (1.0 - reset_terminated.float())
    rew_termination = rew_scale_terminated * reset_terminated.float()
    rew_pole_pos = rew_scale_pole_pos * torch.sum(torch.square(pole_pos).unsqueeze(dim=1), dim=-1)
    rew_cart_vel = rew_scale_cart_vel * torch.sum(torch.abs(cart_vel).unsqueeze(dim=1), dim=-1)
    rew_pole_vel = rew_scale_pole_vel * torch.sum(torch.abs(pole_vel).unsqueeze(dim=1), dim=-1)
    total_reward = rew_alive + rew_termination + rew_pole_pos + rew_cart_vel + rew_pole_vel
    return total_reward
