# Copyright (c) 2022-2025, The Isaac Lab Project Developers (https://github.com/isaac-sim/IsaacLab/blob/main/CONTRIBUTORS.md).
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

actions:
- action_type: JointAction
  clip: null
  dtype: torch.float32
  extras:
    description: Joint action term that applies the processed actions to the articulation's
      joints as position commands.
  full_path: isaaclab.envs.mdp.actions.joint_actions.JointPositionAction
  joint_names:
  - LF_HAA
  - LH_HAA
  - RF_HAA
  - RH_HAA
  - LF_HFE
  - LH_HFE
  - RF_HFE
  - RH_HFE
  - LF_KFE
  - LH_KFE
  - RF_KFE
  - RH_KFE
  mdp_type: Action
  name: joint_position_action
  offset:
  - 0.0
  - 0.0
  - 0.0
  - 0.0
  - 0.4000000059604645
  - -0.4000000059604645
  - 0.4000000059604645
  - -0.4000000059604645
  - -0.800000011920929
  - 0.800000011920929
  - -0.800000011920929
  - 0.800000011920929
  scale: 0.5
  shape:
  - 12
articulations:
  robot:
    default_joint_armature:
    - 0.0
    - 0.0
    - 0.0
    - 0.0
    - 0.0
    - 0.0
    - 0.0
    - 0.0
    - 0.0
    - 0.0
    - 0.0
    - 0.0
    default_joint_damping:
    - 0.0
    - 0.0
    - 0.0
    - 0.0
    - 0.0
    - 0.0
    - 0.0
    - 0.0
    - 0.0
    - 0.0
    - 0.0
    - 0.0
    default_joint_friction:
    - 0.0
    - 0.0
    - 0.0
    - 0.0
    - 0.0
    - 0.0
    - 0.0
    - 0.0
    - 0.0
    - 0.0
    - 0.0
    - 0.0
    default_joint_pos:
    - 0.0
    - 0.0
    - 0.0
    - 0.0
    - 0.4000000059604645
    - -0.4000000059604645
    - 0.4000000059604645
    - -0.4000000059604645
    - -0.800000011920929
    - 0.800000011920929
    - -0.800000011920929
    - 0.800000011920929
    default_joint_pos_limits:
    - - -0.7853984236717224
      - 0.6108654141426086
    - - -0.7853984236717224
      - 0.6108654141426086
    - - -0.6108654141426086
      - 0.7853984236717224
    - - -0.6108654141426086
      - 0.7853984236717224
    - - -9.42477798461914
      - 9.42477798461914
    - - -9.42477798461914
      - 9.42477798461914
    - - -9.42477798461914
      - 9.42477798461914
    - - -9.42477798461914
      - 9.42477798461914
    - - -9.42477798461914
      - 9.42477798461914
    - - -9.42477798461914
      - 9.42477798461914
    - - -9.42477798461914
      - 9.42477798461914
    - - -9.42477798461914
      - 9.42477798461914
    default_joint_stiffness:
    - 0.0
    - 0.0
    - 0.0
    - 0.0
    - 0.0
    - 0.0
    - 0.0
    - 0.0
    - 0.0
    - 0.0
    - 0.0
    - 0.0
    default_joint_vel:
    - 0.0
    - 0.0
    - 0.0
    - 0.0
    - 0.0
    - 0.0
    - 0.0
    - 0.0
    - 0.0
    - 0.0
    - 0.0
    - 0.0
    joint_names:
    - LF_HAA
    - LH_HAA
    - RF_HAA
    - RH_HAA
    - LF_HFE
    - LH_HFE
    - RF_HFE
    - RH_HFE
    - LF_KFE
    - LH_KFE
    - RF_KFE
    - RH_KFE
observations:
  policy:
  - dtype: torch.float32
    extras:
      axes:
      - X
      - Y
      - Z
      description: Root linear velocity in the asset's root frame.
      modifiers: null
      units: m/s
    full_path: isaaclab.envs.mdp.observations.base_lin_vel
    mdp_type: Observation
    name: base_lin_vel
    observation_type: RootState
    overloads:
      clip: null
      flatten_history_dim: true
      history_length: 0
      scale: null
    shape:
    - 3
  - dtype: torch.float32
    extras:
      axes:
      - X
      - Y
      - Z
      description: Root angular velocity in the asset's root frame.
      modifiers: null
      units: rad/s
    full_path: isaaclab.envs.mdp.observations.base_ang_vel
    mdp_type: Observation
    name: base_ang_vel
    observation_type: RootState
    overloads:
      clip: null
      flatten_history_dim: true
      history_length: 0
      scale: null
    shape:
    - 3
  - dtype: torch.float32
    extras:
      axes:
      - X
      - Y
      - Z
      description: Gravity projection on the asset's root frame.
      modifiers: null
      units: m/s^2
    full_path: isaaclab.envs.mdp.observations.projected_gravity
    mdp_type: Observation
    name: projected_gravity
    observation_type: RootState
    overloads:
      clip: null
      flatten_history_dim: true
      history_length: 0
      scale: null
    shape:
    - 3
  - dtype: torch.float32
    extras:
      description: The generated command from command term in the command manager
        with the given name.
      modifiers: null
    full_path: isaaclab.envs.mdp.observations.generated_commands
    mdp_type: Observation
    name: generated_commands
    observation_type: Command
    overloads:
      clip: null
      flatten_history_dim: true
      history_length: 0
      scale: null
    shape:
    - 3
  - dtype: torch.float32
    extras:
      description: 'The joint positions of the asset w.r.t. the default joint positions.
        Note: Only the joints configured in :attr:`asset_cfg.joint_ids` will have
        their positions returned.'
      modifiers: null
      units: rad
    full_path: isaaclab.envs.mdp.observations.joint_pos_rel
    joint_names:
    - LF_HAA
    - LH_HAA
    - RF_HAA
    - RH_HAA
    - LF_HFE
    - LH_HFE
    - RF_HFE
    - RH_HFE
    - LF_KFE
    - LH_KFE
    - RF_KFE
    - RH_KFE
    joint_pos_offsets:
    - 0.0
    - 0.0
    - 0.0
    - 0.0
    - 0.4000000059604645
    - -0.4000000059604645
    - 0.4000000059604645
    - -0.4000000059604645
    - -0.800000011920929
    - 0.800000011920929
    - -0.800000011920929
    - 0.800000011920929
    mdp_type: Observation
    name: joint_pos_rel
    observation_type: JointState
    overloads:
      clip: null
      flatten_history_dim: true
      history_length: 0
      scale: null
    shape:
    - 12
  - dtype: torch.float32
    extras:
      description: 'The joint velocities of the asset w.r.t. the default joint velocities.
        Note: Only the joints configured in :attr:`asset_cfg.joint_ids` will have
        their velocities returned.'
      modifiers: null
      units: rad/s
    full_path: isaaclab.envs.mdp.observations.joint_vel_rel
    joint_names:
    - LF_HAA
    - LH_HAA
    - RF_HAA
    - RH_HAA
    - LF_HFE
    - LH_HFE
    - RF_HFE
    - RH_HFE
    - LF_KFE
    - LH_KFE
    - RF_KFE
    - RH_KFE
    joint_vel_offsets:
    - 0.0
    - 0.0
    - 0.0
    - 0.0
    - 0.0
    - 0.0
    - 0.0
    - 0.0
    - 0.0
    - 0.0
    - 0.0
    - 0.0
    mdp_type: Observation
    name: joint_vel_rel
    observation_type: JointState
    overloads:
      clip: null
      flatten_history_dim: true
      history_length: 0
      scale: null
    shape:
    - 12
  - dtype: torch.float32
    extras:
      description: The last input action to the environment. The name of the action
        term for which the action is required. If None, the entire action tensor is
        returned.
      modifiers: null
    full_path: isaaclab.envs.mdp.observations.last_action
    mdp_type: Observation
    name: last_action
    observation_type: Action
    overloads:
      clip: null
      flatten_history_dim: true
      history_length: 0
      scale: null
    shape:
    - 12
scene:
  decimation: 4
  dt: 0.02
  physics_dt: 0.005
