# Copyright (c) 2022-2025, The Isaac Lab Project Developers (https://github.com/isaac-sim/IsaacLab/blob/main/CONTRIBUTORS.md).
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

"""
倒立摆平衡环境。

该模块包含倒立摆（Cartpole）平衡任务的实现，这是强化学习中的经典控制问题。
倒立摆任务要求智能体学会通过左右移动小车来保持杆子的平衡。

包含的环境变体：
- 基础倒立摆环境：使用状态观察
- RGB相机倒立摆环境：使用RGB图像观察
- 深度相机倒立摆环境：使用深度图像观察

这些环境展示了Isaac Lab中不同观察模态的使用方法。
"""

import gymnasium as gym

from . import agents

##
# 注册Gym环境。
##

gym.register(
    id="Isaac-Cartpole-Direct-v0",  # 基础倒立摆环境ID
    entry_point=f"{__name__}.cartpole_env:CartpoleEnv",  # 环境类入口点
    disable_env_checker=True,  # 禁用环境检查器
    kwargs={
        "env_cfg_entry_point": f"{__name__}.cartpole_env:CartpoleEnvCfg",  # 环境配置入口点
        "rl_games_cfg_entry_point": f"{agents.__name__}:rl_games_ppo_cfg.yaml",  # RL Games PPO配置
        "rsl_rl_cfg_entry_point": f"{agents.__name__}.rsl_rl_ppo_cfg:CartpolePPORunnerCfg",  # RSL RL PPO配置
        "skrl_cfg_entry_point": f"{agents.__name__}:skrl_ppo_cfg.yaml",  # SKRL PPO配置
        "sb3_cfg_entry_point": f"{agents.__name__}:sb3_ppo_cfg.yaml",  # Stable Baselines3 PPO配置
    },
)

gym.register(
    id="Isaac-Cartpole-RGB-Camera-Direct-v0",  # RGB相机倒立摆环境ID
    entry_point=f"{__name__}.cartpole_camera_env:CartpoleCameraEnv",  # 相机环境类入口点
    disable_env_checker=True,  # 禁用环境检查器
    kwargs={
        "env_cfg_entry_point": f"{__name__}.cartpole_camera_env:CartpoleRGBCameraEnvCfg",  # RGB相机配置
        "rl_games_cfg_entry_point": f"{agents.__name__}:rl_games_camera_ppo_cfg.yaml",  # RL Games相机PPO配置
        "skrl_cfg_entry_point": f"{agents.__name__}:skrl_camera_ppo_cfg.yaml",  # SKRL相机PPO配置
    },
)

gym.register(
    id="Isaac-Cartpole-Depth-Camera-Direct-v0",  # 深度相机倒立摆环境ID
    entry_point=f"{__name__}.cartpole_camera_env:CartpoleCameraEnv",  # 相机环境类入口点
    disable_env_checker=True,  # 禁用环境检查器
    kwargs={
        "env_cfg_entry_point": f"{__name__}.cartpole_camera_env:CartpoleDepthCameraEnvCfg",  # 深度相机配置
        "rl_games_cfg_entry_point": f"{agents.__name__}:rl_games_camera_ppo_cfg.yaml",  # RL Games相机PPO配置
        "skrl_cfg_entry_point": f"{agents.__name__}:skrl_camera_ppo_cfg.yaml",  # SKRL相机PPO配置
    },
)
