# Copyright (c) 2022-2025, The Isaac Lab Project Developers (https://github.com/isaac-sim/IsaacLab/blob/main/CONTRIBUTORS.md).
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

"""
四旋翼无人机环境。

该模块实现了四旋翼无人机的飞行控制任务。无人机需要学会通过控制四个
螺旋桨的推力来实现稳定飞行、悬停和轨迹跟踪等复杂的空中机动。

环境特点：
- 四旋翼无人机飞行控制
- 连续动作空间：四个螺旋桨推力控制
- 6自由度空中运动（位置和姿态）
- 奖励函数：鼓励目标跟踪，惩罚过度倾斜和能耗
- 终止条件：无人机坠毁或严重偏离飞行区域
"""

import gymnasium as gym

from . import agents

##
# 注册Gym环境
##

gym.register(
    id="Isaac-Quadcopter-Direct-v0",
    entry_point=f"{__name__}.quadcopter_env:QuadcopterEnv",
    disable_env_checker=True,
    kwargs={
        "env_cfg_entry_point": f"{__name__}.quadcopter_env:QuadcopterEnvCfg",  # 环境配置入口点
        "rl_games_cfg_entry_point": f"{agents.__name__}:rl_games_ppo_cfg.yaml",  # RL Games PPO配置
        "rsl_rl_cfg_entry_point": f"{agents.__name__}.rsl_rl_ppo_cfg:QuadcopterPPORunnerCfg",  # RSL RL PPO配置
        "skrl_cfg_entry_point": f"{agents.__name__}:skrl_ppo_cfg.yaml",  # SKRL PPO配置
    },
)
