# Copyright (c) 2022-2025, The Isaac Lab Project Developers (https://github.com/isaac-sim/IsaacLab/blob/main/CONTRIBUTORS.md).
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

"""
Shadow Hand灵巧手环境。

该模块实现了Shadow Hand五指灵巧手的精细操作任务。灵巧手需要学会使用
多个手指协调操作物体，包括抓取、旋转、重新定位等复杂的手内操作技能。

环境特点：
- 高自由度灵巧手控制（20+关节）
- 精细的手指协调和物体操作
- 多种任务变体：基础版本、OpenAI版本、视觉版本
- 支持不同的神经网络架构：前馈网络、LSTM
- 奖励函数：鼓励精确的物体姿态控制
"""

import gymnasium as gym

from . import agents

##
# 注册Gym环境
##

inhand_task_entry = "isaaclab_tasks.direct.inhand_manipulation"

# 基础Shadow Hand立方体重定位任务
gym.register(
    id="Isaac-Repose-Cube-Shadow-Direct-v0",
    entry_point=f"{inhand_task_entry}.inhand_manipulation_env:InHandManipulationEnv",
    disable_env_checker=True,
    kwargs={
        "env_cfg_entry_point": f"{__name__}.shadow_hand_env_cfg:ShadowHandEnvCfg",  # 环境配置
        "rl_games_cfg_entry_point": f"{agents.__name__}:rl_games_ppo_cfg.yaml",  # RL Games PPO配置
        "rsl_rl_cfg_entry_point": f"{agents.__name__}.rsl_rl_ppo_cfg:ShadowHandPPORunnerCfg",  # RSL RL PPO配置
        "skrl_cfg_entry_point": f"{agents.__name__}:skrl_ppo_cfg.yaml",  # SKRL PPO配置
    },
)

# OpenAI风格Shadow Hand任务（前馈网络版本）
gym.register(
    id="Isaac-Repose-Cube-Shadow-OpenAI-FF-Direct-v0",
    entry_point=f"{inhand_task_entry}.inhand_manipulation_env:InHandManipulationEnv",
    disable_env_checker=True,
    kwargs={
        "env_cfg_entry_point": f"{__name__}.shadow_hand_env_cfg:ShadowHandOpenAIEnvCfg",  # OpenAI风格配置
        "rl_games_cfg_entry_point": f"{agents.__name__}:rl_games_ppo_ff_cfg.yaml",  # 前馈网络PPO配置
        "rsl_rl_cfg_entry_point": f"{agents.__name__}.rsl_rl_ppo_cfg:ShadowHandAsymFFPPORunnerCfg",  # 非对称前馈PPO
        "skrl_cfg_entry_point": f"{agents.__name__}:skrl_ff_ppo_cfg.yaml",  # SKRL前馈PPO配置
    },
)

# OpenAI风格Shadow Hand任务（LSTM网络版本）
gym.register(
    id="Isaac-Repose-Cube-Shadow-OpenAI-LSTM-Direct-v0",
    entry_point=f"{inhand_task_entry}.inhand_manipulation_env:InHandManipulationEnv",
    disable_env_checker=True,
    kwargs={
        "env_cfg_entry_point": f"{__name__}.shadow_hand_env_cfg:ShadowHandOpenAIEnvCfg",  # OpenAI风格配置
        "rl_games_cfg_entry_point": f"{agents.__name__}:rl_games_ppo_lstm_cfg.yaml",  # LSTM网络PPO配置
    },
)

### 视觉版本环境

# Shadow Hand视觉操作任务
gym.register(
    id="Isaac-Repose-Cube-Shadow-Vision-Direct-v0",
    entry_point=f"{__name__}.shadow_hand_vision_env:ShadowHandVisionEnv",
    disable_env_checker=True,
    kwargs={
        "env_cfg_entry_point": f"{__name__}.shadow_hand_vision_env:ShadowHandVisionEnvCfg",  # 视觉环境配置
        "rsl_rl_cfg_entry_point": f"{agents.__name__}.rsl_rl_ppo_cfg:ShadowHandVisionFFPPORunnerCfg",  # 视觉前馈PPO
        "rl_games_cfg_entry_point": f"{agents.__name__}:rl_games_ppo_vision_cfg.yaml",  # 视觉PPO配置
    },
)

# Shadow Hand视觉操作任务（演示版本）
gym.register(
    id="Isaac-Repose-Cube-Shadow-Vision-Direct-Play-v0",
    entry_point=f"{__name__}.shadow_hand_vision_env:ShadowHandVisionEnv",
    disable_env_checker=True,
    kwargs={
        "env_cfg_entry_point": f"{__name__}.shadow_hand_vision_env:ShadowHandVisionEnvPlayCfg",  # 演示配置
        "rsl_rl_cfg_entry_point": f"{agents.__name__}.rsl_rl_ppo_cfg:ShadowHandVisionFFPPORunnerCfg",  # 视觉前馈PPO
        "rl_games_cfg_entry_point": f"{agents.__name__}:rl_games_ppo_vision_cfg.yaml",  # 视觉PPO配置
    },
)
