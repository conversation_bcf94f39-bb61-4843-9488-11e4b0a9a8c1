# Copyright (c) 2022-2025, The Isaac Lab Project Developers (https://github.com/isaac-sim/IsaacLab/blob/main/CONTRIBUTORS.md).
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

"""
人形机器人运动环境。

该模块实现了人形机器人的双足行走控制任务。人形机器人需要学会保持平衡
并进行稳定的双足行走，这是机器人学中最具挑战性的控制问题之一。

环境特点：
- 双足人形机器人控制
- 高维连续动作空间：多关节协调控制
- 复杂的平衡和步态学习
- 奖励函数：鼓励前进运动，保持直立姿态
- 终止条件：机器人跌倒或严重偏离目标轨迹
"""

import gymnasium as gym

from . import agents

##
# 注册Gym环境
##

gym.register(
    id="Isaac-Humanoid-Direct-v0",
    entry_point=f"{__name__}.humanoid_env:HumanoidEnv",
    disable_env_checker=True,
    kwargs={
        "env_cfg_entry_point": f"{__name__}.humanoid_env:HumanoidEnvCfg",  # 环境配置入口点
        "rl_games_cfg_entry_point": f"{agents.__name__}:rl_games_ppo_cfg.yaml",  # RL Games PPO配置
        "rsl_rl_cfg_entry_point": f"{agents.__name__}.rsl_rl_ppo_cfg:HumanoidPPORunnerCfg",  # RSL RL PPO配置
        "skrl_cfg_entry_point": f"{agents.__name__}:skrl_ppo_cfg.yaml",  # SKRL PPO配置
    },
)
