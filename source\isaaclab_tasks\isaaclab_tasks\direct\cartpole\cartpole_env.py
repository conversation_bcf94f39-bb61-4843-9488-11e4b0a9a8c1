# Copyright (c) 2022-2025, The Isaac Lab Project Developers (https://github.com/isaac-sim/IsaacLab/blob/main/CONTRIBUTORS.md).
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

"""
倒立摆平衡环境实现。

该模块实现了经典的倒立摆（Cartpole）控制问题，这是强化学习中的基准任务之一。
智能体需要学会通过左右移动小车来保持杆子的平衡。

环境特点：
- 连续动作空间：对小车施加力
- 4维观察空间：杆子角度、杆子角速度、小车位置、小车速度
- 终止条件：杆子倾斜超过90度或小车移动超出边界
- 奖励函数：鼓励保持平衡，惩罚过大的运动
"""

from __future__ import annotations

import math
import torch
from collections.abc import Sequence

from isaaclab_assets.robots.cartpole import CARTPOLE_CFG

import isaaclab.sim as sim_utils
from isaaclab.assets import Articulation, ArticulationCfg
from isaaclab.envs import DirectRLEnv, DirectRLEnvCfg
from isaaclab.scene import InteractiveSceneCfg
from isaaclab.sim import SimulationCfg
from isaaclab.sim.spawners.from_files import GroundPlaneCfg, spawn_ground_plane
from isaaclab.utils import configclass
from isaaclab.utils.math import sample_uniform


@configclass
class CartpoleEnvCfg(DirectRLEnvCfg):
    """倒立摆环境配置类。

    该配置类定义了倒立摆环境的所有参数，包括仿真设置、机器人配置、
    场景设置、重置参数和奖励权重等。
    """

    # 环境参数
    decimation = 2  # 控制频率降采样因子（仿真步数/控制步数）
    episode_length_s = 5.0  # 每个回合的最大时长（秒）
    action_scale = 100.0  # 动作缩放因子 [N]，将归一化动作转换为实际力
    action_space = 1  # 动作空间维度（小车的力）
    observation_space = 4  # 观察空间维度（杆角度、杆角速度、车位置、车速度）
    state_space = 0  # 状态空间维度（此环境中未使用）

    # 仿真配置
    sim: SimulationCfg = SimulationCfg(dt=1 / 120, render_interval=decimation)

    # 机器人配置
    robot_cfg: ArticulationCfg = CARTPOLE_CFG.replace(prim_path="/World/envs/env_.*/Robot")
    cart_dof_name = "slider_to_cart"  # 小车关节名称
    pole_dof_name = "cart_to_pole"  # 杆子关节名称

    # 场景配置
    scene: InteractiveSceneCfg = InteractiveSceneCfg(
        num_envs=4096,  # 并行环境数量
        env_spacing=4.0,  # 环境间距（米）
        replicate_physics=True,  # 复制物理设置
        clone_in_fabric=True  # 在Fabric中克隆
    )

    # 重置参数
    max_cart_pos = 3.0  # 小车超出此位置时重置环境 [m]
    initial_pole_angle_range = [-0.25, 0.25]  # 重置时杆子角度的采样范围 [rad]

    # 奖励权重
    rew_scale_alive = 1.0  # 存活奖励权重
    rew_scale_terminated = -2.0  # 终止惩罚权重
    rew_scale_pole_pos = -1.0  # 杆子位置惩罚权重
    rew_scale_cart_vel = -0.01  # 小车速度惩罚权重
    rew_scale_pole_vel = -0.005  # 杆子速度惩罚权重


class CartpoleEnv(DirectRLEnv):
    """倒立摆环境类。

    该类实现了倒立摆平衡任务，继承自DirectRLEnv。智能体需要学会
    通过对小车施加水平力来保持杆子的平衡。

    Attributes:
        cfg: 环境配置
        _cart_dof_idx: 小车关节索引
        _pole_dof_idx: 杆子关节索引
        action_scale: 动作缩放因子
        joint_pos: 关节位置
        joint_vel: 关节速度
    """
    cfg: CartpoleEnvCfg

    def __init__(self, cfg: CartpoleEnvCfg, render_mode: str | None = None, **kwargs):
        """初始化倒立摆环境。

        Args:
            cfg: 环境配置
            render_mode: 渲染模式
            **kwargs: 其他关键字参数
        """
        super().__init__(cfg, render_mode, **kwargs)

        # 获取关节索引
        self._cart_dof_idx, _ = self.cartpole.find_joints(self.cfg.cart_dof_name)
        self._pole_dof_idx, _ = self.cartpole.find_joints(self.cfg.pole_dof_name)
        self.action_scale = self.cfg.action_scale

        # 缓存关节状态引用以提高性能
        self.joint_pos = self.cartpole.data.joint_pos
        self.joint_vel = self.cartpole.data.joint_vel

    def _setup_scene(self):
        """设置仿真场景。

        创建倒立摆机器人、地面、光照等场景元素，并配置环境克隆。
        """
        # 创建倒立摆机器人
        self.cartpole = Articulation(self.cfg.robot_cfg)
        # 添加地面
        spawn_ground_plane(prim_path="/World/ground", cfg=GroundPlaneCfg())
        # 克隆和复制环境
        self.scene.clone_environments(copy_from_source=False)
        # CPU仿真需要显式过滤碰撞
        if self.device == "cpu":
            self.scene.filter_collisions(global_prim_paths=[])
        # 将关节机器人添加到场景
        self.scene.articulations["cartpole"] = self.cartpole
        # 添加光照
        light_cfg = sim_utils.DomeLightCfg(intensity=2000.0, color=(0.75, 0.75, 0.75))
        light_cfg.func("/World/Light", light_cfg)

    def _pre_physics_step(self, actions: torch.Tensor) -> None:
        """物理步骤前的预处理。

        将归一化的动作缩放到实际的力值。

        Args:
            actions: 归一化的动作张量，形状为 (num_envs, action_dim)
        """
        self.actions = self.action_scale * actions.clone()

    def _apply_action(self) -> None:
        """应用动作到仿真中。

        将计算得到的力施加到小车的关节上。
        """
        self.cartpole.set_joint_effort_target(self.actions, joint_ids=self._cart_dof_idx)

    def _get_observations(self) -> dict:
        """获取环境观察。

        返回包含杆子角度、杆子角速度、小车位置和小车速度的观察向量。

        Returns:
            包含策略观察的字典，观察向量形状为 (num_envs, 4)
        """
        obs = torch.cat(
            (
                self.joint_pos[:, self._pole_dof_idx[0]].unsqueeze(dim=1),  # 杆子角度
                self.joint_vel[:, self._pole_dof_idx[0]].unsqueeze(dim=1),  # 杆子角速度
                self.joint_pos[:, self._cart_dof_idx[0]].unsqueeze(dim=1),  # 小车位置
                self.joint_vel[:, self._cart_dof_idx[0]].unsqueeze(dim=1),  # 小车速度
            ),
            dim=-1,
        )
        observations = {"policy": obs}
        return observations

    def _get_rewards(self) -> torch.Tensor:
        """计算奖励。

        奖励函数包含以下组成部分：
        - 存活奖励：鼓励保持平衡
        - 终止惩罚：惩罚环境终止
        - 杆子位置惩罚：惩罚杆子偏离垂直位置
        - 小车速度惩罚：惩罚过大的小车速度
        - 杆子速度惩罚：惩罚过大的杆子角速度

        Returns:
            每个环境的总奖励，形状为 (num_envs,)
        """
        total_reward = compute_rewards(
            self.cfg.rew_scale_alive,
            self.cfg.rew_scale_terminated,
            self.cfg.rew_scale_pole_pos,
            self.cfg.rew_scale_cart_vel,
            self.cfg.rew_scale_pole_vel,
            self.joint_pos[:, self._pole_dof_idx[0]],
            self.joint_vel[:, self._pole_dof_idx[0]],
            self.joint_pos[:, self._cart_dof_idx[0]],
            self.joint_vel[:, self._cart_dof_idx[0]],
            self.reset_terminated,
        )
        return total_reward

    def _get_dones(self) -> tuple[torch.Tensor, torch.Tensor]:
        """检查环境终止条件。

        环境在以下情况下终止：
        1. 小车位置超出边界 (±max_cart_pos)
        2. 杆子角度超过±90度
        3. 达到最大回合长度

        Returns:
            terminated: 因失败而终止的环境掩码
            time_out: 因超时而终止的环境掩码
        """
        # 更新关节状态
        self.joint_pos = self.cartpole.data.joint_pos
        self.joint_vel = self.cartpole.data.joint_vel

        # 检查超时
        time_out = self.episode_length_buf >= self.max_episode_length - 1

        # 检查越界条件
        # 小车位置越界
        out_of_bounds = torch.any(torch.abs(self.joint_pos[:, self._cart_dof_idx]) > self.cfg.max_cart_pos, dim=1)
        # 杆子角度越界（超过±90度）
        out_of_bounds = out_of_bounds | torch.any(torch.abs(self.joint_pos[:, self._pole_dof_idx]) > math.pi / 2, dim=1)

        return out_of_bounds, time_out

    def _reset_idx(self, env_ids: Sequence[int] | None):
        """重置指定的环境。

        将指定环境重置到初始状态，包括随机化杆子的初始角度。

        Args:
            env_ids: 要重置的环境索引，如果为None则重置所有环境
        """
        if env_ids is None:
            env_ids = self.cartpole._ALL_INDICES
        super()._reset_idx(env_ids)

        # 设置初始关节位置（随机化杆子角度）
        joint_pos = self.cartpole.data.default_joint_pos[env_ids]
        joint_pos[:, self._pole_dof_idx] += sample_uniform(
            self.cfg.initial_pole_angle_range[0] * math.pi,
            self.cfg.initial_pole_angle_range[1] * math.pi,
            joint_pos[:, self._pole_dof_idx].shape,
            joint_pos.device,
        )
        joint_vel = self.cartpole.data.default_joint_vel[env_ids]

        # 设置根状态（位置和方向）
        default_root_state = self.cartpole.data.default_root_state[env_ids]
        default_root_state[:, :3] += self.scene.env_origins[env_ids]

        # 更新缓存的关节状态
        self.joint_pos[env_ids] = joint_pos
        self.joint_vel[env_ids] = joint_vel

        # 将状态写入仿真
        self.cartpole.write_root_pose_to_sim(default_root_state[:, :7], env_ids)
        self.cartpole.write_root_velocity_to_sim(default_root_state[:, 7:], env_ids)
        self.cartpole.write_joint_state_to_sim(joint_pos, joint_vel, None, env_ids)


@torch.jit.script
def compute_rewards(
    rew_scale_alive: float,
    rew_scale_terminated: float,
    rew_scale_pole_pos: float,
    rew_scale_cart_vel: float,
    rew_scale_pole_vel: float,
    pole_pos: torch.Tensor,
    pole_vel: torch.Tensor,
    cart_pos: torch.Tensor,
    cart_vel: torch.Tensor,
    reset_terminated: torch.Tensor,
):
    """计算倒立摆环境的奖励。

    该函数使用JIT编译以提高性能。奖励函数包含多个组成部分：

    Args:
        rew_scale_alive: 存活奖励的缩放因子
        rew_scale_terminated: 终止惩罚的缩放因子
        rew_scale_pole_pos: 杆子位置惩罚的缩放因子
        rew_scale_cart_vel: 小车速度惩罚的缩放因子
        rew_scale_pole_vel: 杆子速度惩罚的缩放因子
        pole_pos: 杆子角度位置
        pole_vel: 杆子角速度
        cart_pos: 小车位置
        cart_vel: 小车速度
        reset_terminated: 环境终止掩码

    Returns:
        每个环境的总奖励
    """
    # 存活奖励：鼓励保持平衡
    rew_alive = rew_scale_alive * (1.0 - reset_terminated.float())

    # 终止惩罚：惩罚环境失败
    rew_termination = rew_scale_terminated * reset_terminated.float()

    # 杆子位置惩罚：惩罚杆子偏离垂直位置
    rew_pole_pos = rew_scale_pole_pos * torch.sum(torch.square(pole_pos).unsqueeze(dim=1), dim=-1)

    # 小车速度惩罚：惩罚过大的小车运动
    rew_cart_vel = rew_scale_cart_vel * torch.sum(torch.abs(cart_vel).unsqueeze(dim=1), dim=-1)

    # 杆子速度惩罚：惩罚过大的杆子运动
    rew_pole_vel = rew_scale_pole_vel * torch.sum(torch.abs(pole_vel).unsqueeze(dim=1), dim=-1)

    # 总奖励
    total_reward = rew_alive + rew_termination + rew_pole_pos + rew_cart_vel + rew_pole_vel
    return total_reward
