# Copyright (c) 2022-2025, The Isaac Lab Project Developers (https://github.com/isaac-sim/IsaacLab/blob/main/CONTRIBUTORS.md).
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

"""用于递归导入包中所有模块的实用工具子模块。

该模块提供了自动导入包中所有子模块的功能，简化了包的初始化过程。
它特别适用于需要自动注册大量环境或配置的场景。
"""

from __future__ import annotations

import importlib
import pkgutil
import sys


def import_packages(package_name: str, blacklist_pkgs: list[str] | None = None):
    """递归导入包中的所有子包。

    使用此函数递归导入包中的所有子包比手动导入每个子包更容易。

    它替代了在每个包的``__init__.py``文件顶部需要的以下代码片段：

    .. code-block:: python

        import .locomotion.velocity
        import .manipulation.reach
        import .manipulation.lift

    Args:
        package_name: 包名称。要导入的根包的名称。
        blacklist_pkgs: 要跳过的黑名单包列表。默认为None，
            表示没有包被列入黑名单。黑名单中的包及其子包将被跳过。
    """
    # 默认黑名单
    if blacklist_pkgs is None:
        blacklist_pkgs = []
    # 导入包本身
    package = importlib.import_module(package_name)
    # 导入所有Python文件
    for _ in _walk_packages(package.__path__, package.__name__ + ".", blacklist_pkgs=blacklist_pkgs):
        pass


"""
内部辅助函数。
"""


def _walk_packages(
    path: str | None = None,
    prefix: str = "",
    onerror: callable | None = None,
    blacklist_pkgs: list[str] | None = None,
):
    """递归地为路径上的所有模块生成ModuleInfo，如果path为None，则为所有可访问的模块。

    Note:
        此函数是原始``pkgutil.walk_packages``函数的修改版本。它添加了
        ``blacklist_pkgs``参数来跳过黑名单包。更多详细信息请参考原始的
        ``pkgutil.walk_packages``函数。

    Args:
        path: 要搜索的路径列表。如果为None，则搜索所有可访问的模块。
        prefix: 模块名称的前缀。
        onerror: 导入错误时调用的回调函数。
        blacklist_pkgs: 要跳过的黑名单包列表。
    """
    if blacklist_pkgs is None:
        blacklist_pkgs = []

    def seen(p, m={}):
        """检查路径是否已经被访问过，避免重复遍历。"""
        if p in m:
            return True
        m[p] = True  # noqa: R503

    for info in pkgutil.iter_modules(path, prefix):
        # 检查是否在黑名单中
        if any([black_pkg_name in info.name for black_pkg_name in blacklist_pkgs]):
            continue

        # 生成模块信息
        yield info

        # 如果是包，则递归处理
        if info.ispkg:
            try:
                __import__(info.name)
            except Exception:
                if onerror is not None:
                    onerror(info.name)
                else:
                    raise
            else:
                path = getattr(sys.modules[info.name], "__path__", None) or []

                # 不遍历之前已经见过的路径项
                path = [p for p in path if not seen(p)]

                # 递归遍历子包
                yield from _walk_packages(path, info.name + ".", onerror, blacklist_pkgs)
