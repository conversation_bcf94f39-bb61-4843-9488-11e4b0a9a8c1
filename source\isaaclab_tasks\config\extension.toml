[package]

# 注意：使用语义版本控制：https://semver.org/
version = "0.11.0"

# 描述信息
title = "Isaac Lab Environments"  # Isaac Lab环境套件
description="Extension containing suite of environments for robot learning."  # 包含机器人学习环境套件的扩展
readme  = "docs/README.md"  # 说明文档路径
repository = "https://github.com/isaac-sim/IsaacLab"  # 代码仓库地址
category = "robotics"  # 分类：机器人学
keywords = ["robotics", "rl", "il", "learning"]  # 关键词：机器人学、强化学习、模仿学习、学习

[dependencies]
# 依赖项：Isaac Lab核心库和资产库
"isaaclab" = {}
"isaaclab_assets" = {}

[core]
reloadable = false  # 不可重新加载

[[python.module]]
name = "isaaclab_tasks"  # Python模块名称
