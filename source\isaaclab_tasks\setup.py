# Copyright (c) 2022-2025, The Isaac Lab Project Developers (https://github.com/isaac-sim/IsaacLab/blob/main/CONTRIBUTORS.md).
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

"""'isaaclab_tasks' Python包的安装脚本。

该脚本用于安装Isaac Lab任务包，包含了各种机器人学习环境的实现。
它定义了包的依赖项、元数据和安装配置。
"""

import os
import toml

from setuptools import setup

# 从extension.toml文件获取扩展数据
EXTENSION_PATH = os.path.dirname(os.path.realpath(__file__))
# 读取extension.toml文件
EXTENSION_TOML_DATA = toml.load(os.path.join(EXTENSION_PATH, "config", "extension.toml"))

# 安装前所需的最小依赖项
INSTALL_REQUIRES = [
    # 通用依赖
    "numpy<2",  # 数值计算库，版本限制为小于2.0以确保兼容性
    "torch>=2.7",  # PyTorch深度学习框架，最低版本2.7
    "torchvision>=0.14.1",  # 确保与torch 1.13.1的兼容性
    "protobuf>=4.25.8,!=5.26.0",  # 协议缓冲区，排除有问题的5.26.0版本
    # 基础日志记录
    "tensorboard",  # TensorBoard可视化工具
    # 自动化相关
    "scikit-learn",  # 机器学习库
    "numba",  # JIT编译器，用于加速数值计算
]

# PyTorch的CUDA版本下载链接
PYTORCH_INDEX_URL = ["https://download.pytorch.org/whl/cu128"]

# 安装操作配置
setup(
    name="isaaclab_tasks",  # 包名
    author="Isaac Lab Project Developers",  # 作者
    maintainer="Isaac Lab Project Developers",  # 维护者
    url=EXTENSION_TOML_DATA["package"]["repository"],  # 项目URL
    version=EXTENSION_TOML_DATA["package"]["version"],  # 版本号
    description=EXTENSION_TOML_DATA["package"]["description"],  # 描述
    keywords=EXTENSION_TOML_DATA["package"]["keywords"],  # 关键词
    include_package_data=True,  # 包含包数据文件
    python_requires=">=3.10",  # Python版本要求
    install_requires=INSTALL_REQUIRES,  # 安装依赖
    dependency_links=PYTORCH_INDEX_URL,  # 依赖链接
    packages=["isaaclab_tasks"],  # 包列表
    classifiers=[  # 分类器，用于PyPI分类
        "Natural Language :: English",  # 自然语言：英语
        "Programming Language :: Python :: 3.10",  # 编程语言：Python 3.10
        "Programming Language :: Python :: 3.11",  # 编程语言：Python 3.11
        "Isaac Sim :: 4.5.0",  # Isaac Sim版本支持
        "Isaac Sim :: 5.0.0",  # Isaac Sim版本支持
    ],
    zip_safe=False,  # 不安全压缩，因为包含数据文件
)
