Extensions Changelog
====================

All notable changes to this project are documented in this file. The format is based on
`Keep a Changelog <https://keepachangelog.com/en/1.0.0/>`__ and this project adheres to
`Semantic Versioning <https://semver.org/spec/v2.0.0.html>`__. For a broader information
about the changes in the framework, please refer to the
`release notes <https://github.com/isaac-sim/IsaacLab/releases/>`__.

Each extension has its own changelog. The changelog for each extension is located in the
``docs`` directory of the extension. The changelog for each extension is also included in
this changelog to make it easier to find the changelog for a specific extension.

isaaclab
--------------

Extension containing the core framework of Isaac Lab.

.. include:: ../../../source/isaaclab/docs/CHANGELOG.rst
   :start-line: 3


isaaclab_assets
---------------------

Extension for configurations of various assets and sensors for Isaac Lab.

.. include:: ../../../source/isaaclab_assets/docs/CHANGELOG.rst
   :start-line: 3


isaaclab_tasks
--------------------

Extension containing the environments built using Isaac Lab.

.. include:: ../../../source/isaaclab_tasks/docs/CHANGELOG.rst
   :start-line: 3
