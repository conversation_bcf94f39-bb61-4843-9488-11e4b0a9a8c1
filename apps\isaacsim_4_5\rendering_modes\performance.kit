rtx.translucency.enabled = false

rtx.reflections.enabled = false
rtx.reflections.denoiser.enabled = false

rtx.directLighting.sampledLighting.denoisingTechnique = 0
rtx.directLighting.sampledLighting.enabled = false

rtx.sceneDb.ambientLightIntensity = 1.0

rtx.shadows.enabled = true

rtx.indirectDiffuse.enabled = false
rtx.indirectDiffuse.denoiser.enabled = false

rtx.domeLight.upperLowerStrategy = 3

rtx.ambientOcclusion.enabled = false
rtx.ambientOcclusion.denoiserMode = 1

rtx.raytracing.subpixel.mode = 0
rtx.raytracing.cached.enabled = false

# DLSS frame gen does not yet support tiled camera well
rtx-transient.dlssg.enabled = false
rtx-transient.dldenoiser.enabled = false

# Set the DLSS model
rtx.post.dlss.execMode = 0 # can be 0 (Performance), 1 (Balanced), 2 (Quality), or 3 (Auto)

# Avoids replicator warning
rtx.pathtracing.maxSamplesPerLaunch = 1000000

# Avoids silent trimming of tiles
rtx.viewTile.limit = 1000000
