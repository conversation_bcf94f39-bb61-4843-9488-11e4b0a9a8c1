# Copyright (c) 2022-2025, The Isaac Lab Project Developers (https://github.com/isaac-sim/IsaacLab/blob/main/CONTRIBUTORS.md).
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

"""首先启动Isaac Sim仿真器。

该测试模块用于测试Isaac Lab中的Hydra配置系统。它验证Hydra配置的
注册、解析和覆盖功能是否正常工作。
"""

import sys

from isaaclab.app import AppLauncher

# 启动仿真器
app_launcher = AppLauncher(headless=True)
simulation_app = app_launcher.app


"""其余所有内容如下。"""

import functools
from collections.abc import Callable

import hydra
from hydra import compose, initialize
from omegaconf import OmegaConf

from isaaclab.utils import replace_strings_with_slices

import isaaclab_tasks  # noqa: F401
from isaaclab_tasks.utils.hydra import register_task_to_hydra


def hydra_task_config_test(task_name: str, agent_cfg_entry_point: str) -> Callable:
    """从hydra.py的hydra_task_config复制，因为hydra.main需要单一入口点，
    这对多个测试不起作用。在这里，我们用hydra initialize和compose替换hydra.main。

    Args:
        task_name: 任务名称。
        agent_cfg_entry_point: 智能体配置入口点。

    Returns:
        装饰器函数。
    """

    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # 将任务注册到Hydra
            env_cfg, agent_cfg = register_task_to_hydra(task_name, agent_cfg_entry_point)

            # 用initialize和compose替换hydra.main
            with initialize(config_path=None, version_base="1.3"):
                hydra_env_cfg = compose(config_name=task_name, overrides=sys.argv[1:])
                # 转换为原生字典
                hydra_env_cfg = OmegaConf.to_container(hydra_env_cfg, resolve=True)
                # 将字符串替换为切片，因为OmegaConf不支持切片
                hydra_env_cfg = replace_strings_with_slices(hydra_env_cfg)
                # 使用Hydra命令行参数更新配置
                env_cfg.from_dict(hydra_env_cfg["env"])
                if isinstance(agent_cfg, dict):
                    agent_cfg = hydra_env_cfg["agent"]
                else:
                    agent_cfg.from_dict(hydra_env_cfg["agent"])
                # 调用原始函数
                func(env_cfg, agent_cfg, *args, **kwargs)

        return wrapper

    return decorator


def test_hydra():
    """测试Hydra配置系统。

    该测试验证Hydra配置系统能够正确处理各种类型的配置覆盖，
    包括简单值、切片、正则表达式和空值设置。
    """

    # 设置硬编码的命令行参数
    sys.argv = [
        sys.argv[0],
        "env.decimation=42",  # 测试简单的环境修改
        "env.events.physics_material.params.asset_cfg.joint_ids='slice(0 ,1, 2)'",  # 测试切片设置
        "env.scene.robot.init_state.joint_vel={.*: 4.0}",  # 测试正则表达式设置
        "env.rewards.feet_air_time=null",  # 测试设置为空
        "agent.max_iterations=3",  # 测试简单的智能体修改
    ]

    @hydra_task_config_test("Isaac-Velocity-Flat-H1-v0", "rsl_rl_cfg_entry_point")
    def main(env_cfg, agent_cfg):
        # 环境配置验证
        assert env_cfg.decimation == 42
        assert env_cfg.events.physics_material.params["asset_cfg"].joint_ids == slice(0, 1, 2)
        assert env_cfg.scene.robot.init_state.joint_vel == {".*": 4.0}
        assert env_cfg.rewards.feet_air_time is None
        # 智能体配置验证
        assert agent_cfg.max_iterations == 3

    main()
    # 清理
    sys.argv = [sys.argv[0]]
    hydra.core.global_hydra.GlobalHydra.instance().clear()


def test_nested_iterable_dict():
    """测试当字典嵌套在可迭代对象中时的Hydra配置系统。

    该测试验证Hydra配置系统能够正确处理复杂的嵌套数据结构，
    特别是在可迭代对象中嵌套字典的情况。
    """

    @hydra_task_config_test("Isaac-Lift-Cube-Franka-v0", "rsl_rl_cfg_entry_point")
    def main(env_cfg, agent_cfg):
        # 环境配置验证 - 测试嵌套结构
        assert env_cfg.scene.ee_frame.target_frames[0].name == "end_effector"
        assert env_cfg.scene.ee_frame.target_frames[0].offset.pos[2] == 0.1034

    main()
    # 清理
    sys.argv = [sys.argv[0]]
    hydra.core.global_hydra.GlobalHydra.instance().clear()
