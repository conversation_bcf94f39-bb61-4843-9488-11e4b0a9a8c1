# Copyright (c) 2022-2025, The Isaac Lab Project Developers (https://github.com/isaac-sim/IsaacLab/blob/main/CONTRIBUTORS.md).
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

"""
四旋翼无人机环境的RSL RL PPO配置。

该模块定义了使用RSL RL库训练四旋翼无人机飞行控制策略的PPO算法配置。
配置针对四旋翼无人机的连续控制特性进行了优化。
"""

from isaaclab.utils import configclass

from isaaclab_rl.rsl_rl import RslRlOnPolicyRunnerCfg, RslRlPpoActorCriticCfg, RslRlPpoAlgorithmCfg


@configclass
class QuadcopterPPORunnerCfg(RslRlOnPolicyRunnerCfg):
    """四旋翼无人机PPO训练运行器配置。

    该配置类定义了使用RSL RL库训练四旋翼无人机策略的所有参数，
    包括训练步数、网络架构和算法超参数。
    """

    # 训练参数
    num_steps_per_env = 24  # 每个环境每次更新的步数
    max_iterations = 200  # 最大训练迭代次数
    save_interval = 50  # 模型保存间隔
    experiment_name = "quadcopter_direct"  # 实验名称

    # 策略网络配置
    policy = RslRlPpoActorCriticCfg(
        init_noise_std=1.0,  # 初始动作噪声标准差（鼓励探索）
        actor_obs_normalization=False,  # 不对演员网络的观察进行归一化
        critic_obs_normalization=False,  # 不对评论家网络的观察进行归一化
        actor_hidden_dims=[64, 64],  # 演员网络隐藏层维度
        critic_hidden_dims=[64, 64],  # 评论家网络隐藏层维度
        activation="elu",  # 激活函数：ELU（适合连续控制）
    )

    # PPO算法配置
    algorithm = RslRlPpoAlgorithmCfg(
        value_loss_coef=1.0,  # 价值函数损失系数
        use_clipped_value_loss=True,  # 使用裁剪的价值损失
        clip_param=0.2,  # PPO裁剪参数
        entropy_coef=0.0,  # 熵正则化系数（0表示不使用熵正则化）
        num_learning_epochs=5,  # 每次更新的学习轮数
        num_mini_batches=4,  # 小批次数量
        learning_rate=5.0e-4,  # 学习率
        schedule="adaptive",  # 自适应学习率调度
        gamma=0.99,  # 折扣因子
        lam=0.95,  # GAE lambda参数
        desired_kl=0.01,  # 期望的KL散度（用于自适应学习率）
        max_grad_norm=1.0,  # 梯度裁剪的最大范数
    )
