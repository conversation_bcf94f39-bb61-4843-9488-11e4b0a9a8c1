Limitations
===========

During the early development phase of both Newton and this Isaac Lab integration,
you are likely to encounter breaking changes as well as limited documentation.

We do not expect to be able to provide support or debugging assistance until the framework has reached an official release.

Here is a non-exhaustive list of capabilities currently supported in the Newton experimental feature branch grouped by extension:

* isaaclab:
    * Articulation API
    * Contact Sensor
    * Direct & Manager single agent workflows
    * Omniverse Kit visualizer
    * Newton visualizer
* isaaclab_assets:
    * Anymal-D
    * Unitree H1 & G1
    * Toy examples
        * Cartpole
        * Ant
        * Humanoid
* isaaclab_tasks:
    * Direct:
        * Cartpole
        * Ant
        * Humanoid
    * Manager based:
        * Locomotion (velocity flat terrain)
            * Anymal-D
            * Unitree G1
            * Unitree H1

Capabilities beyond the above are not currently available.
We expect to support APIs related to rigid bodies soon in order to unlock manipulation based environments.
